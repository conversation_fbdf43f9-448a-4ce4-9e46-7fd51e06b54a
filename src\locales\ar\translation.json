{"header": {"home": "الرئيسية", "about": "عن", "projects": "المشاريع", "blog": "المدونة", "contact": "اتصل بنا", "language": "EN"}, "hero": {"title": "مهندس نظم ويب مدعوم بالذكاء الاصطناعي", "greeting": "بناء المستقبل مع", "name": "الطيب", "role": "مهندس نظم ويب مدعوم بالذكاء الاصطناعي", "subtitle": "AI-Powered Web Architect", "description": "أقوم بتنسيق أنظمة ويب معقدة من خلال التفكير المنظم والتعاون مع الذكاء الاصطناعي. متخصص في الهياكل القائمة على الأدوار ومنصات إدارة المحتوى والأنظمة الإنتاجية القابلة للتوسع.", "quote": "البرمجة لم تعد تُكتب، بل تُدار.", "quoteAr": "Coding is no longer typed — it's orchestrated.", "viewProjects": "استكشف الأنظمة", "contactMe": "لنبني معاً", "scrollDown": "اكتش<PERSON> المزيد", "skills": {"systemArchitecture": "هندسة الأنظمة", "aiCollaboration": "التعاون مع الذكاء الاصطناعي", "roleBasedSystems": "الأنظمة القائمة على الأدوار", "cmsplatforms": "منصات إدارة المحتوى", "productionGrade": "أنظمة إنتاجية متقدمة"}}, "about": {"title": "هندسة الأنظمة", "subtitle": "مهندس نظم ويب مدعوم بالذكاء الاصطناعي يبني أنظمة معقدة وقابلة للتوسع من خلال التنسيق الذكي.", "story": {"title": "منهجيتي", "p1": "لا أكتب الكود سطراً بسطر—بل أنسق أنظمة ذكية. باستخدام التعاون مع الذكاء الاصطناعي مع GPT وClaude، أصمم منصات ويب معقدة تتعامل مع منطق الأعمال الحقيقي.", "p2": "خبرتي تكمن في بناء أنظمة قائمة على الأدوار مع هياكل الموزعين والإداريين والمستخدمين. لقد أنشأت منصات إدارة محتوى تمنح العملاء حرية مستوى WordPress مع الحفاظ على الأمان والأداء على مستوى المؤسسات.", "p3": "كل نظام أبنيه يتضمن نماذج مخصصة وترقيم صفحات متقدم وتصفية ذكية ومنطق بحث قوي وحماية شاملة لواجهات برمجة التطبيقات. أعمل مع Next.js وMongoDB وSupabase وقواعد بيانات SQL والتقنيات الحديثة.", "p4": "أقود الهندسة المعمارية ورؤية المنتج بينما أستفيد من الذكاء الاصطناعي لتسريع التطوير. النتيجة؟ أنظمة إنتاجية مع تجربة مستخدم قوية ودعم متعدد اللغات وواجهات RTL تعمل فعلياً في العالم الحقيقي."}, "development": {"title": "قدرات الأنظمة", "cms": "منصات إدارة المحتوى", "cmsDesc": "حرية مستوى WordPress مع أمان المؤسسات", "roleBasedSystems": "الهندسة القائمة على الأدوار", "roleBasedSystemsDesc": "منطق وصلاحيات الموزعين والإداريين والمستخدمين", "dataManagement": "إدارة البيانات", "dataManagementDesc": "تصفية متقدمة وبحث وترقيم وواجهات برمجة تطبيقات", "multilingual": "أنظمة متعددة اللغات", "multilingualDesc": "واجهات عربية وإنجليزية وRTL تعمل بفعالية"}, "skills": {"title": "المجموعة التقنية"}, "viewProjects": "استكشف أنظمتي"}, "skills": {"title": "المجموعة التقنية", "subtitle": "تقنيات إنتاجية متقدمة أستخدمها لبناء أنظمة ذكية وقابلة للتوسع", "categories": {"architecture": "هندسة الأنظمة", "frontend": "تنسيق الواجهة الأمامية", "backend": "البنية التحتية الخلفية", "database": "إدارة البيانات", "ai": "التعاون مع الذكاء الاصطناعي", "deployment": "النشر الإنتاجي"}, "stack": {"nextjs": "Next.js", "react": "React 18", "typescript": "TypeScript", "tailwind": "Tailwind CSS", "shadcn": "Shadcn UI", "mongodb": "MongoDB", "supabase": "Supabase", "sql": "قواعد بيانات SQL", "firebase": "Firebase", "nodejs": "Node.js", "api": "واجهات برمجة REST", "auth": "المصادقة", "gpt": "تكامل GPT", "claude": "<PERSON>", "prompt": "هندسة الإرشادات", "multilingual": "متعد<PERSON> اللغات/RTL", "responsive": "التصميم المتجاوب", "performance": "تحسين الأداء"}}, "systemArchitecture": {"title": "منهجية هندسة الأنظمة", "subtitle": "كيف أنسق أنظمة ويب معقدة من خلال التفكير المنظم والتعاون مع الذكاء الاصطناعي", "processTitle": "عملية الهندسة المعمارية", "processSubtitle": "من المفهوم إلى النظام الإنتاجي المتقدم", "stackTitle": "مجموعة تقنية إنتاجية متقدمة", "features": {"roleBasedAccess": {"title": "التحكم في الوصول القائم على الأدوار", "desc": "هياكل الموزعين والإداريين والمستخدمين مع صلاحيات دقيقة وتدفقات مصادقة آمنة."}, "apiProtection": {"title": "أمان وحماية واجهات برمجة التطبيقات", "desc": "حماية شاملة لواجهات برمجة التطبيقات مع تحديد المعدل والمصادقة وطبقات التحقق من البيانات."}, "dataManagement": {"title": "إدارة البيانات المتقدمة", "desc": "تصفية ذكية ومنطق بحث وترقيم صفحات واستعلامات قاعدة بيانات محسنة للأداء."}, "customForms": {"title": "أنظمة النماذج الديناميكية", "desc": "منشئو نماذج مخصصة مع التحقق والمنطق الشرطي وتدفقات معالجة البيانات السلسة."}, "multilingual": {"title": "دعم متعدد اللغات وRTL", "desc": "دعم أصلي للعربية والإنجليزية مع تخطيطات RTL مناسبة والتوطين الثقافي."}, "aiIntegration": {"title": "التطوير المدعوم بالذكاء الاصطناعي", "desc": "تعاون استراتيجي مع الذكاء الاصطناعي للتطوير المتسارع مع الحفاظ على سلامة الهندسة المعمارية."}}, "process": {"analysis": {"title": "تحليل الأعمال", "desc": "غوص عميق في المتطلبات وأدوار المستخدمين وتعقيد النظام لتحديد أساس الهندسة المعمارية."}, "architecture": {"title": "تصميم النظام", "desc": "إنشاء هندسة معمارية قابلة للتوسع مع منطق قائم على الأدوار وتدفق البيانات واعتبارات الأمان."}, "aiCollaboration": {"title": "التعاون مع الذكاء الاصطناعي", "desc": "الاستفادة من GPT وClaude للتطوير السريع مع قيادة الرؤية التقنية والقرارات."}, "implementation": {"title": "النشر الإنتاجي", "desc": "نشر أنظمة قوية ومختبرة مع المراقبة وتحسين الأداء وبروتوكولات الصيانة."}}}, "projects": {"title": "مشاريعي", "subtitle": "مجموعة مختارة من أعمالي الأخيرة. استخدم عوامل التصفية لتصفح المشاريع حسب الفئة.", "projectTitles": {"watches": "متجر ساعات فاخرة | Watches Store", "vusto": "فوستو - مطعم راقٍ", "reelify": "ريليفاي - منصة استكشاف الأفلام", "elegance": "إليجانس - منصة أزياء عربية حديثة", "flux": "فلكس ديجيتال - سوق الخدمات الرقمية", "pubg": "متجر RNG لببجي - سوق الألعاب"}, "viewAll": "عرض جميع المشاريع", "liveDemo": "عرض حي", "source": "المصدر", "featured": "مميز", "preview": "معاينة", "fullPreview": "معاينة كاملة", "openInNewTab": "فتح في علامة تبويب جديدة", "visitWebsite": "زيارة الموقع", "refresh": "تحديث", "mobileView": "عرض الجوال", "desktopView": "عرض سطح المكتب", "details": "التفاصيل", "viewDetails": "عرض التفاصيل", "moreInfo": "مزيد من المعلومات", "explore": "استكشاف", "about": "نبذة", "overview": "نظرة عامة", "features": "الميزات الرئيسية", "technologies": "التقنيات المستخدمة", "vustoDescription": "موقع مطعم أنيق وعصري مصمم لعرض التميز الطهي وتوفير تجربة استثنائية للمستخدم مع تصميم جميل ومتجاوب.", "vustoFeature1": "تصميم بصري مذهل مع تأثيرات متوازية", "vustoFeature2": "تخطيط متجاوب لجميع الأجهزة", "vustoFeature3": "قائمة طعام تفاعلية مع عرض جميل", "vustoFeature4": "نظام حجز مع نموذج اتصال", "vustoFeature5": "شهادات وتقييمات العملاء", "vustoFeature6": "رسوم متحركة لطيفة عند التمرير", "vustoTech": "تم بناؤه باستخدام React و TypeScript، باستخدام Vite للتطوير. يتميز بمكونات shadcn/ui، و Tailwind CSS للتصميم، و Framer Motion للرسوم المتحركة، و React Router للتنقل.", "fluxDescription": "منصة تجارة إلكترونية متطورة ومتجاوبة متخصصة في الخدمات الرقمية مع دعم متعدد اللغات، وتخصيص ديناميكي للسمات، ونظام سلة تسوق قوي.", "fluxFeature1": "تصميم متجاوب مُحسن لجميع الأجهزة", "fluxFeature2": "دعم متعدد اللغات مع إمكانيات RTL", "fluxFeature3": "تخصيص السمات مع أوضاع فاتحة/داكنة", "fluxFeature4": "واجهة مستخدم تفاعلية مع رسوم متحركة ثلاثية الأبعاد", "fluxFeature5": "وظائف التجارة الإلكترونية مع سلة تسوق مستمرة", "fluxFeature6": "لوحة تحكم المسؤول مع التحليلات", "fluxTech": "تم بناؤه باستخدام React و TypeScript باستخدام Vite. يتميز بـ Tailwind CSS و Shadcn UI للتصميم، و Framer Motion للرسوم المتحركة، و Context API لإدارة الحالة. تم تحسينه مع تقسيم الكود والتحميل الكسول.", "pubgDescription": "سوق شامل للاعبي ببجي موبايل، يوفر منصة آمنة وغنية بالميزات لشراء وبيع واكتشاف موارد الألعاب مع ميزات مدعومة بالذكاء الاصطناعي وأداء محسن.", "pubgFeature1": "سوق حسابات ببجي مع نظام التحقق", "pubgFeature2": "متجر UC بأسعار تنافسية وتسليم فوري", "pubgFeature3": "قسم التعديلات والهاكات مع التحقق من التوافق", "pubgFeature4": "مدونة ومجتمع لأحدث أخبار وتحديثات ببجي", "pubgFeature5": "وضع داكن/فاتح مع سمة واجهة قابلة للتخصيص", "pubgFeature6": "دعم متعدد اللغات مع إمكانيات RTL", "pubgFeature7": "تحليل محتوى مدعوم بالذكاء الاصطناعي وتوصيات الأسعار", "pubgFeature8": "تحسين SEO شامل مع بيانات منظمة", "pubgTech": "تم بناؤه باستخدام React 18 و TypeScript و Vite للواجهة الأمامية. يستخدم Tailwind CSS و Shadcn UI و Framer Motion للتصميم. يتميز بخدمات Firebase (Firestore، المصادقة، التخزين، الاستضافة) للخدمات الخلفية. ينفذ React Router و React Query و Context API لإدارة الحالة. يتكامل مع OpenRouter API لقدرات الذكاء الاصطناعي ويتضمن تحسين SEO شامل.", "watchesDescription": "منصة تجارة إلكترونية حديثة وفاخرة متخصصة في الساعات الفاخرة، مبنية باستخدام React و TypeScript ومكونات Shadcn UI لتجربة تسوق أنيقة.", "watchesOverview": "متجر الساعات هو منصة تجارة إلكترونية حديثة وفاخرة متخصصة في الساعات الفاخرة. تم بناؤه باستخدام React وTypeScript ومكونات Shadcn UI، ويقدم تجربة تسوق أنيقة مع ميزات شاملة تشمل تصفية المنتجات، وظيفة قائمة الرغبات، سلة تسوق كاملة الوظائف مع أدوات التحكم في الكمية، إدارة ملف تعريف المستخدم، وتصميم متجاوب مُحسن لجميع الأجهزة. تستفيد المنصة من React 18، وReact Router v6، وTailwindCSS، وContext API لإدارة الحالة، وTanStack React Query لجلب البيانات، وReact Hook Form مع تحقق Zod. تم تحسين واجهة المستخدم باستخدام Radix UI primitives، وEmbla Carousel، ورسوم Framer Motion المتحركة، وإشعارات Sonner لإنشاء تجربة تسوق متميزة لعشاق الساعات الفاخرة.", "watchesFeature1": "تصفح الساعات الفاخرة المميزة مع معلومات مفصلة عن المنتج", "watchesFeature2": "تصفية المنتجات حسب الفئة ونطاق السعر والمزيد", "watchesFeature3": "حفظ العناصر المفضلة في قائمة الرغبات", "watchesFeature4": "سلة تسوق كاملة الوظائف مع أدوات التحكم في الكمية", "watchesFeature5": "إدارة ملف تعريف المستخدم", "watchesFeature6": "تصميم متجاوب لجميع الأجهزة", "watchesFeature7": "واجهة مستخدم حديثة مع مكونات Shadcn", "watchesTech": "تم بناؤه باستخدام React 18 و TypeScript باستخدام Vite. يتميز بـ React Router v6 و TailwindCSS مع Shadcn UI و Context API لإدارة الحالة و TanStack React Query لجلب البيانات و React Hook Form مع تحقق Zod. تم تحسين واجهة المستخدم باستخدام Radix UI primitives و Embla Carousel و Framer Motion للرسوم المتحركة و Sonner toasts.", "eleganceDescription": "منصة تجارة إلكترونية متطورة مبنية لعلامة أزياء عربية فاخرة مع دعم كامل للغة العربية واتجاه الكتابة من اليمين إلى اليسار، مع واجهة مستخدم فاخرة وأنيقة تجسد هوية العلامة التجارية.", "eleganceFeature1": "تعريب كامل مع دعم اتجاه الكتابة من اليمين إلى اليسار", "eleganceFeature2": "كتالوج منتجات شامل مع 48 منتجاً عبر 6 فئات", "eleganceFeature3": "تصفية ذكية مع عينات ألوان وأسماء ألوان عربية", "eleganceFeature4": "وظيفة قائمة الرغبات مع تخزين محلي", "eleganceFeature5": "وضع داكن/فاتح مع واجهة مستخدم محسنة في كلا الوضعين", "eleganceFeature6": "تجربة جوال محسنة مع بحث ومرشحات مخصصة", "eleganceFeature7": "تحسينات الأداء مع useMemo و useCallback", "eleganceTech": "تم بناؤه باستخدام React 18 و TypeScript و Vite. يتميز بـ React Router DOM و TanStack React Query و Tailwind CSS ومكونات Shadcn UI (مبنية على Radix UI). يستخدم أيقونات Lucide React والتخزين المحلي لسلة التسوق وقائمة الرغبات. تم تحسينه باستخدام تقسيم الكود وتأخير البحث وحالات تحميل هيكلية مناسبة.", "reelifyDescription": "منصة بسيطة وقوية لاستكشاف أحدث الأفلام ومشاهدة الإعلانات الترويجية ومتابعة التحديثات على الممثلين وصناع الأفلام مع واجهة بديهية.", "reelifyFeature1": "مشاهدة إعلانات الأفلام الترويجية مباشرة من قاعدة بيانات الأفلام", "reelifyFeature2": "البحث عن الأف<PERSON>ام حسب العنوان أو النوع أو التقييم", "reelifyFeature3": "البقاء على اطلاع بأحدث إصدارات الأفلام", "reelifyFeature4": "استكشاف ملفات تعريف الممثلين وصناع الأفلام مع السير الذاتية", "reelifyFeature5": "الحصول على رؤى شباك التذاكر حول أداء الفيلم والأرباح", "reelifyTech": "تم بناؤه باستخدام TypeScript و React و Vite لتجربة مستخدم سريعة ومتجاوبة. متكامل مع واجهة برمجة تطبيقات قاعدة بيانات الأفلام للحصول على معلومات شاملة عن الأفلام وبث الإعلانات الترويجية."}, "contact": {"title": "تواصل معي", "subtitle": "هل لديك مشروع في ذهنك أو ترغب في مناقشة فرص التعاون؟ أود أن أسمع منك!", "letsCollaborate": "لنتعاون معًا", "collaborationText": "أنا مطور متحمس متخصص في تطوير الويب ودمج الذكاء الاصطناعي. سواء كنت بحاجة إلى موقع ويب مذهل، أو تطبيق ويب معقد، أو حلول مدعومة بالذكاء الاصطناعي، أنا هنا لتحويل رؤيتك إلى واقع.", "myDetails": "تفاصيل الاتصال الخاصة بي", "emailLabel": "الب<PERSON>يد الإلكتروني", "phoneLabel": "الهاتف", "whatsappLabel": "واتساب", "socialProfiles": "الملفات الاجتماعية", "messageSent": "تم إرسال الرسالة!", "thankYouMessage": "شكرًا {{name}}! سأرد عليك عبر {{email}} قريبًا.", "form": {"name": "اسمك", "email": "بريدك الإلكتروني", "subject": "الموضوع", "message": "رسالتك", "send": "إرسال الرسالة", "sending": "جاري الإرسال..."}}, "footer": {"building": "بناء تجارب ويب ذكية", "rights": "جميع الحقوق محفوظة.", "quickLinks": "روابط سريعة", "newsletter": {"title": "اشترك في النشرة الإخبارية", "description": "ابق على اطلاع بأحدث مشاريعي ومقالاتي.", "placeholder": "بريدك الإلكتروني", "button": "اشترك", "thanks": "شكراً لاشتراكك!", "confirmation": "ستتلقى الآن تحديثات حول أحدث مشاريعي ومقالاتي."}}, "blogPostNotFound": {"title": "لم يتم العثور على المقال", "message": "المقال الذي تبحث عنه غير موجود أو تمت إزالته.", "backButton": "العودة إلى المدونة"}, "blog": {"latestInsights": "أحد<PERSON> المقالات", "articlesDescription": "مقالات عن هندسة الإرشادات، تطوير الويب، ثقافة البرمجة، والمزيد.", "searchPlaceholder": "البحث في المقالات...", "all": "الكل", "featured": "مميز", "trending": "رائج", "popularTags": "العلامات الشائعة", "readMore": "قراءة المزيد", "minRead": "دقيقة قراءة", "clearFilters": "م<PERSON><PERSON> المرشحات", "noResults": "لم يتم العثور على نتائج", "tryDifferent": "جرب كلمات بحث مختلفة أو قم بإزالة المرشحات", "activeFilters": "المرشحات النشطة", "stayUpdated": "ابق على اطلاع", "newsletterDesc": "احصل على أحدث المقالات والدروس والرؤى مباشرة إلى بريدك الإلكتروني.", "emailPlaceholder": "أدخل بريدك الإلكتروني", "subscribe": "اشترك", "resetFilters": "إعادة تعيين البحث والمرشحات"}}