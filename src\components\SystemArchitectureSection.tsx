import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from 'react-i18next';
import { getFontClass } from '../i18n';
import { 
  Users, 
  Shield, 
  Database, 
  Layers, 
  Workflow, 
  Globe,
  Brain,
  Zap,
  Settings,
  Lock
} from "lucide-react";

const systemFeatures = [
  {
    icon: Users,
    titleKey: "systemArchitecture.features.roleBasedAccess.title",
    descKey: "systemArchitecture.features.roleBasedAccess.desc",
    color: "text-blue-400"
  },
  {
    icon: Shield,
    titleKey: "systemArchitecture.features.apiProtection.title", 
    descKey: "systemArchitecture.features.apiProtection.desc",
    color: "text-green-400"
  },
  {
    icon: Database,
    titleKey: "systemArchitecture.features.dataManagement.title",
    descKey: "systemArchitecture.features.dataManagement.desc", 
    color: "text-purple-400"
  },
  {
    icon: Workflow,
    titleKey: "systemArchitecture.features.customForms.title",
    descKey: "systemArchitecture.features.customForms.desc",
    color: "text-orange-400"
  },
  {
    icon: Globe,
    titleKey: "systemArchitecture.features.multilingual.title",
    descKey: "systemArchitecture.features.multilingual.desc",
    color: "text-cyan-400"
  },
  {
    icon: Brain,
    titleKey: "systemArchitecture.features.aiIntegration.title",
    descKey: "systemArchitecture.features.aiIntegration.desc",
    color: "text-neon"
  }
];

const architectureFlow = [
  {
    step: "01",
    titleKey: "systemArchitecture.process.analysis.title",
    descKey: "systemArchitecture.process.analysis.desc"
  },
  {
    step: "02", 
    titleKey: "systemArchitecture.process.architecture.title",
    descKey: "systemArchitecture.process.architecture.desc"
  },
  {
    step: "03",
    titleKey: "systemArchitecture.process.aiCollaboration.title", 
    descKey: "systemArchitecture.process.aiCollaboration.desc"
  },
  {
    step: "04",
    titleKey: "systemArchitecture.process.implementation.title",
    descKey: "systemArchitecture.process.implementation.desc"
  }
];

export default function SystemArchitectureSection() {
  const { t } = useTranslation();

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const stagger = {
    visible: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <section className="py-20 bg-gradient-to-b from-dark to-black/40">
      <div className="container">
        {/* Header */}
        <motion.div 
          className="text-center max-w-4xl mx-auto mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          transition={{ duration: 0.6 }}
        >
          <h2 className={`text-3xl md:text-5xl font-bold mb-4 ${getFontClass()}`}>
            {t('systemArchitecture.title', 'System Architecture Approach')}
          </h2>
          <div className="h-1 w-20 bg-neon mx-auto mb-6 rounded-full"></div>
          <p className="text-gray-300 text-lg leading-relaxed">
            {t('systemArchitecture.subtitle', 'How I orchestrate complex web systems through structured thinking and AI collaboration')}
          </p>
        </motion.div>

        {/* System Features Grid */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-20"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={stagger}
        >
          {systemFeatures.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <motion.div
                key={index}
                variants={fadeInUp}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="bg-dark/80 border border-white/10 h-full transform transition-all duration-500 hover:-translate-y-2 hover:shadow-[0_0_30px_rgba(0,255,247,0.15)] hover:border-neon/30">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="p-2 bg-dark rounded-lg">
                        <IconComponent className={`h-6 w-6 ${feature.color}`} />
                      </div>
                      <CardTitle className={`text-lg ${getFontClass()}`}>
                        {t(feature.titleKey)}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400 leading-relaxed">
                      {t(feature.descKey)}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Architecture Process */}
        <motion.div
          className="max-w-5xl mx-auto"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="text-center mb-12">
            <h3 className={`text-2xl md:text-3xl font-bold mb-4 ${getFontClass()}`}>
              {t('systemArchitecture.processTitle', 'My Architecture Process')}
            </h3>
            <p className="text-gray-400">
              {t('systemArchitecture.processSubtitle', 'From concept to production-grade system')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {architectureFlow.map((step, index) => (
              <motion.div
                key={index}
                className="relative"
                variants={fadeInUp}
                transition={{ duration: 0.6, delay: index * 0.15 }}
              >
                <Card className="bg-dark/60 border border-white/10 h-full transform transition-all duration-500 hover:-translate-y-1 hover:border-neon/30">
                  <CardHeader className="text-center">
                    <div className="mx-auto mb-4 w-12 h-12 bg-neon/10 rounded-full flex items-center justify-center">
                      <span className="text-neon font-bold text-lg">{step.step}</span>
                    </div>
                    <CardTitle className={`text-lg ${getFontClass()}`}>
                      {t(step.titleKey)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400 text-sm leading-relaxed text-center">
                      {t(step.descKey)}
                    </p>
                  </CardContent>
                </Card>
                
                {/* Connection Line */}
                {index < architectureFlow.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-3 w-6 h-0.5 bg-gradient-to-r from-neon/50 to-transparent"></div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Tech Stack Highlight */}
        <motion.div
          className="mt-20 text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="max-w-4xl mx-auto">
            <h3 className={`text-xl md:text-2xl font-bold mb-6 ${getFontClass()}`}>
              {t('systemArchitecture.stackTitle', 'Production-Grade Technology Stack')}
            </h3>
            <div className="flex flex-wrap justify-center gap-3">
              {['Next.js', 'MongoDB', 'Supabase', 'SQL', 'TypeScript', 'Tailwind', 'Shadcn UI', 'Firebase', 'Node.js', 'GPT', 'Claude AI'].map((tech) => (
                <Badge 
                  key={tech}
                  variant="outline" 
                  className="bg-dark/50 border-neon/30 text-neon hover:bg-neon/10 transition-colors px-4 py-2 text-sm"
                >
                  {tech}
                </Badge>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
