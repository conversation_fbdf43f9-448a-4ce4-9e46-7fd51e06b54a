
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useTranslation } from 'react-i18next';
import { getFontClass } from '../i18n';
import {
  Database,
  Globe,
  Layers,
  Brain,
  Shield,
  Zap,
  Code,
  Server,
  Smartphone,
  Settings
} from "lucide-react";

const skillCategories = [
  {
    id: "architecture",
    icon: Layers,
    skills: ["System Design", "Role-Based Architecture", "API Design", "Database Schema"]
  },
  {
    id: "frontend",
    icon: Globe,
    skills: ["Next.js", "React 18", "TypeScript", "Tailwind CSS", "Shadcn UI"]
  },
  {
    id: "backend",
    icon: Server,
    skills: ["Node.js", "REST APIs", "Authentication", "Performance Optimization"]
  },
  {
    id: "database",
    icon: Database,
    skills: ["MongoDB", "Supabase", "SQL Databases", "Firebase"]
  },
  {
    id: "ai",
    icon: Brain,
    skills: ["GPT Integration", "Claude AI", "Prompt Engineering", "AI Collaboration"]
  },
  {
    id: "deployment",
    icon: Zap,
    skills: ["Multilingual/RTL", "Responsive Design", "Production Deployment", "CMS Platforms"]
  }
];

export default function SkillsSection() {
  const { t } = useTranslation();

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const stagger = {
    visible: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <section className="py-20 bg-gradient-to-b from-black/40 to-dark">
      <div className="container">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          transition={{ duration: 0.6 }}
        >
          <h2 className={`text-3xl md:text-5xl font-bold mb-4 ${getFontClass()}`}>{t('skills.title')}</h2>
          <div className="h-1 w-20 bg-neon mx-auto mb-6 rounded-full"></div>
          <p className="text-gray-300 text-lg">
            {t('skills.subtitle')}
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={stagger}
        >
          {skillCategories.map((category, index) => {
            const IconComponent = category.icon;
            return (
              <motion.div
                key={category.id}
                variants={fadeInUp}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="bg-dark/80 border border-white/10 h-full transform transition-all duration-500 hover:-translate-y-2 hover:shadow-[0_0_30px_rgba(0,255,247,0.15)] hover:border-neon/30">
                  <CardHeader className="text-center pb-4">
                    <div className="mx-auto mb-4 p-3 bg-neon/10 rounded-full w-fit">
                      <IconComponent className="h-8 w-8 text-neon" />
                    </div>
                    <CardTitle className={`text-xl text-gradient ${getFontClass()}`}>
                      {t(`skills.categories.${category.id}`)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {category.skills.map((skill) => (
                        <Badge
                          key={skill}
                          variant="outline"
                          className="bg-dark/50 border-white/20 text-gray-300 hover:border-neon/50 hover:text-neon transition-colors"
                        >
                          {t(`skills.stack.${skill.toLowerCase().replace(/[^a-z0-9]/g, '')}`, skill)}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
}


