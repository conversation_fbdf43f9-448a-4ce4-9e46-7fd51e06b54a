{"header": {"home": "Home", "about": "About", "projects": "Projects", "blog": "Blog", "contact": "Contact", "language": "عربي"}, "hero": {"title": "AI-Powered Web Architect", "greeting": "Building the future with", "name": "Altayeb", "role": "AI-Powered Web Architect", "subtitle": "مهندس نظم ويب مدعوم بالذكاء الاصطناعي", "description": "I orchestrate complex web systems through structured thinking and AI collaboration. Specializing in role-based architectures, CMS platforms, and production-grade systems that scale.", "quote": "Coding is no longer typed — it's orchestrated.", "quoteAr": "البرمجة لم تعد تُكتب، بل تُدار.", "viewProjects": "Explore Systems", "contactMe": "Let's Architect", "scrollDown": "Discover More", "skills": {"systemArchitecture": "System Architecture", "aiCollaboration": "AI Collaboration", "roleBasedSystems": "Role-Based Systems", "cmsplatforms": "CMS Platforms", "productionGrade": "Production-Grade Systems"}}, "about": {"title": "System Architecture", "subtitle": "AI-Powered Web Architect building complex, scalable systems through intelligent orchestration.", "story": {"title": "My Approach", "p1": "I don't write code line by line—I orchestrate intelligent systems. Using AI collaboration with GPT and Claude, I architect complex web platforms that handle real-world business logic.", "p2": "My expertise lies in building role-based systems with distributor, admin, and user hierarchies. I've created CMS platforms that give clients WordPress-level freedom while maintaining enterprise-grade security and performance.", "p3": "Every system I build includes custom forms, advanced pagination, intelligent filtering, robust search logic, and comprehensive API protection. I work with Next.js, MongoDB, Supabase, SQL databases, and modern stacks.", "p4": "I lead the architecture and product vision while leveraging AI to accelerate development. The result? Production-grade systems with solid UX, multilingual support, and RTL interfaces that actually work in the real world."}, "development": {"title": "System Capabilities", "cms": "CMS Platforms", "cmsDesc": "WordPress-level freedom with enterprise security", "roleBasedSystems": "Role-Based Architecture", "roleBasedSystemsDesc": "Distributor, admin, user logic and permissions", "dataManagement": "Data Management", "dataManagementDesc": "Advanced filtering, search, pagination, and APIs", "multilingual": "Multilingual Systems", "multilingualDesc": "Arabic, English, RTL interfaces that work"}, "skills": {"title": "Technical Stack"}, "viewProjects": "Explore My Systems"}, "skills": {"title": "Technical Stack", "subtitle": "Production-grade technologies I use to build scalable, intelligent systems", "categories": {"architecture": "System Architecture", "frontend": "Frontend Orchestration", "backend": "Backend Infrastructure", "database": "Data Management", "ai": "AI Collaboration", "deployment": "Production Deployment"}, "stack": {"nextjs": "Next.js", "react": "React 18", "typescript": "TypeScript", "tailwind": "Tailwind CSS", "shadcn": "Shadcn UI", "mongodb": "MongoDB", "supabase": "Supabase", "sql": "SQL Databases", "firebase": "Firebase", "nodejs": "Node.js", "api": "REST APIs", "auth": "Authentication", "gpt": "GPT Integration", "claude": "<PERSON>", "prompt": "Prompt Engineering", "multilingual": "i18n/RTL", "responsive": "Responsive Design", "performance": "Performance Optimization"}}, "systemArchitecture": {"title": "System Architecture Approach", "subtitle": "How I orchestrate complex web systems through structured thinking and AI collaboration", "processTitle": "My Architecture Process", "processSubtitle": "From concept to production-grade system", "stackTitle": "Production-Grade Technology Stack", "features": {"roleBasedAccess": {"title": "Role-Based Access Control", "desc": "Distributor, admin, and user hierarchies with granular permissions and secure authentication flows."}, "apiProtection": {"title": "API Security & Protection", "desc": "Comprehensive API protection with rate limiting, authentication, and data validation layers."}, "dataManagement": {"title": "Advanced Data Management", "desc": "Intelligent filtering, search logic, pagination, and optimized database queries for performance."}, "customForms": {"title": "Dynamic Form Systems", "desc": "Custom form builders with validation, conditional logic, and seamless data processing workflows."}, "multilingual": {"title": "Multilingual & RTL Support", "desc": "Native Arabic and English support with proper RTL layouts and cultural localization."}, "aiIntegration": {"title": "AI-Powered Development", "desc": "Strategic AI collaboration for accelerated development while maintaining architectural integrity."}}, "process": {"analysis": {"title": "Business Analysis", "desc": "Deep dive into requirements, user roles, and system complexity to define the architecture foundation."}, "architecture": {"title": "System Design", "desc": "Create scalable architecture with role-based logic, data flow, and security considerations."}, "aiCollaboration": {"title": "AI Collaboration", "desc": "Leverage GPT and Claude for rapid development while leading the technical vision and decisions."}, "implementation": {"title": "Production Deployment", "desc": "Deploy robust, tested systems with monitoring, performance optimization, and maintenance protocols."}}}, "projects": {"title": "Featured Projects", "subtitle": "Explore my portfolio of AI-enhanced applications and integrated solutions, showcasing expertise in prompt engineering, e-commerce, and multilingual development.", "projectTitles": {"watches": "Watches Store | Luxury Timepieces", "vusto": "Vusto - Fine Dining Restaurant", "reelify": "Reelify - Movie Exploration Platform", "elegance": "Elegance - Modern Arabic Fashion Platform", "flux": "Flux Digital Canvas - Digital Services Marketplace", "pubg": "PUBG RNG Store - Gaming Marketplace"}, "viewAll": "View All Projects", "liveDemo": "Live Demo", "source": "Source", "featured": "Featured", "preview": "Preview", "fullPreview": "Full Preview", "openInNewTab": "Open in new tab", "visitWebsite": "Visit Website", "refresh": "Refresh", "mobileView": "Mobile View", "desktopView": "Desktop View", "details": "Details", "viewDetails": "View Details", "moreInfo": "More Info", "explore": "Explore", "about": "About", "overview": "Overview", "features": "Key Features", "technologies": "Technologies Used", "vustoDescription": "Elegant, modern restaurant website designed to showcase culinary excellence and provide an exceptional user experience with beautiful, responsive design.", "vustoFeature1": "Stunning visual design with parallax effects", "vustoFeature2": "Responsive layout for all devices", "vustoFeature3": "Interactive menu with beautiful presentation", "vustoFeature4": "Reservation system with contact form", "vustoFeature5": "Testimonials and customer reviews", "vustoFeature6": "Subtle scroll reveal animations", "vustoTech": "Built with React and TypeScript, using Vite for development. Features shadcn/ui components, Tailwind CSS for styling, Framer Motion for animations, and React Router for navigation.", "fluxDescription": "Sophisticated, responsive e-commerce platform specializing in digital services with multi-language support, dynamic theme customization, and a robust shopping cart system.", "fluxFeature1": "Responsive design optimized for all devices", "fluxFeature2": "Multilingual support with RTL capabilities", "fluxFeature3": "Theme customization with light/dark modes", "fluxFeature4": "Interactive UI with 3D animations", "fluxFeature5": "E-commerce functionality with persistent cart", "fluxFeature6": "Admin dashboard with analytics", "fluxTech": "Built with React and TypeScript using Vite. Features Tailwind CSS and Shadcn UI for styling, Framer Motion for animations, and Context API for state management. Optimized with code splitting and lazy loading.", "pubgDescription": "Comprehensive marketplace for PUBG Mobile players, providing a secure and feature-rich platform to buy, sell, and discover gaming resources with AI-powered features and optimized performance.", "pubgFeature1": "PUBG Accounts Marketplace with verification system", "pubgFeature2": "UC Store with competitive pricing and instant delivery", "pubgFeature3": "Mods & Hacks section with compatibility verification", "pubgFeature4": "Blog & Community for latest PUBG news and updates", "pubgFeature5": "Dark/Light Mode with customizable interface theme", "pubgFeature6": "Multi-language support with RTL capabilities", "pubgFeature7": "AI-powered content analysis and price recommendations", "pubgFeature8": "Comprehensive SEO optimization with structured data", "pubgTech": "Built with React 18, TypeScript, and Vite for the frontend. Uses Tailwind CSS, Shadcn UI, and Framer Motion for styling. Features Firebase (Firestore, Authentication, Storage, Hosting) for backend services. Implements React Router, React Query, and Context API for state management. Integrates with OpenRouter API for AI capabilities and includes comprehensive SEO optimization.", "watchesDescription": "A modern, luxurious e-commerce platform specializing in high-end watches, built with React, TypeScript, and Shadcn UI components for an elegant shopping experience.", "watchesOverview": "The Watches Store is a modern, luxurious e-commerce platform specializing in high-end watches. Built with React, TypeScript, and Shadcn UI components, it delivers an elegant shopping experience with comprehensive features including product filtering, wishlist functionality, a fully functional shopping cart with quantity controls, user profile management, and responsive design optimized for all devices. The platform leverages React 18, React Router v6, TailwindCSS, Context API for state management, TanStack React Query for data fetching, and React Hook Form with Zod validation. The UI is enhanced with Radix UI primitives, Embla Carousel, Framer Motion animations, and Sonner toasts to create a premium shopping experience for luxury watch enthusiasts.", "watchesFeature1": "Browse premium luxury watches with detailed product information", "watchesFeature2": "Filter products by category, price range, and more", "watchesFeature3": "Save favorite items to wishlist", "watchesFeature4": "Fully functional shopping cart with quantity controls", "watchesFeature5": "User profile management", "watchesFeature6": "Responsive design for all devices", "watchesFeature7": "Modern UI with Shadcn components", "watchesTech": "Built with React 18 and TypeScript using Vite. Features React Router v6, TailwindCSS with Shadcn UI, Context API for state management, TanStack React Query for data fetching, and React Hook Form with Zod validation. UI enhanced with Radix UI primitives, Embla Carousel, Framer Motion animations, and Sonner toasts.", "eleganceDescription": "A sophisticated e-commerce platform built for a premium Arabic fashion brand with full RTL support, featuring a luxurious and elegant UI that embodies the brand's identity.", "eleganceFeature1": "Complete Arabic localization with RTL support", "eleganceFeature2": "Extensive product catalog with 48 products across 6 categories", "eleganceFeature3": "Smart filtering with color swatches and Arabic color names", "eleganceFeature4": "Wishlist functionality with local storage persistence", "eleganceFeature5": "Dark/Light mode with refined UI in both themes", "eleganceFeature6": "Optimized mobile experience with dedicated search and filters", "eleganceFeature7": "Performance optimizations with useMemo and useCallback", "eleganceTech": "Built with React 18, TypeScript, and Vite. Features React Router DOM, TanStack React Query, Tailwind CSS, and Shadcn UI Components (built on Radix UI). Uses Lucide React Icons and Local Storage for cart and wishlist persistence. Optimized with code splitting, search debouncing, and proper skeleton loading states.", "reelifyDescription": "A simple yet powerful platform for exploring the latest movies, watching trailers, and tracking updates on actors and filmmakers with an intuitive interface.", "reelifyFeature1": "Watch movie trailers streamed directly from a movie database", "reelifyFeature2": "Search for movies by title, genre, or rating", "reelifyFeature3": "Stay updated on latest film releases", "reelifyFeature4": "Explore actor and filmmaker profiles with biographies", "reelifyFeature5": "Get box office insights on movie performance and earnings", "reelifyTech": "Built with TypeScript, React, and Vite for a fast and responsive user experience. Integrated with Movie Database API for comprehensive film information and trailer streaming."}, "contact": {"title": "Get In Touch", "subtitle": "Have a project in mind or want to discuss collaboration opportunities? I'd love to hear from you!", "letsCollaborate": "Let's Collaborate", "collaborationText": "I'm a passionate developer specializing in web development and AI integration. Whether you need a stunning website, a complex web application, or AI-powered solutions, I'm here to bring your vision to life.", "myDetails": "My Contact Details", "emailLabel": "Email", "phoneLabel": "Phone", "whatsappLabel": "WhatsApp", "socialProfiles": "Social Profiles", "messageSent": "Message sent!", "thankYouMessage": "Thanks {{name}}! I'll get back to you at {{email}} soon.", "form": {"name": "Your Name", "email": "Your Email", "subject": "Subject", "message": "Your Message", "send": "Send Message", "sending": "Sending..."}}, "footer": {"building": "Building intelligent web experiences", "rights": "All rights reserved.", "quickLinks": "Quick Links", "newsletter": {"title": "Subscribe to Newsletter", "description": "Stay updated with my latest projects and articles.", "placeholder": "Your email address", "button": "Subscribe", "thanks": "Thanks for subscribing!", "confirmation": "You'll now receive updates on my latest projects and articles."}}}