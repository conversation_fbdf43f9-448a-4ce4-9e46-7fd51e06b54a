import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Github, Linkedin, ArrowDown, Code, Monitor, Database, Play, Download, Copy, Check, Terminal, Maximize, X, Eye, EyeOff, RefreshCw, Globe, FileCode, FileText, Archive } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useTranslation } from 'react-i18next';
import { getFontClass } from '../i18n';
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { ParticleButton } from "@/components/ui/particle-button";

// CSS to hide all scrollbars in the HeroSection
const heroSectionStyle = `
  #home * {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
  }
  
  #home *::-webkit-scrollbar {
    display: none !important;
  }
  
  /* Hide scrollbars in Live Preview iframe */
  #home iframe {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
  }
  
  #home iframe::-webkit-scrollbar {
    display: none !important;
  }
  
  /* Additional styles for iframe content */
  #home iframe {
    overflow: hidden !important;
  }
`;

export default function HeroSection() {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
  const heroRef = useRef<HTMLElement>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [copied, setCopied] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  
  // View switching
  const [activeView, setActiveView] = useState<'code' | 'terminal'>('code');
  
  // Terminal states  
  const [terminalCommand, setTerminalCommand] = useState('');
  const [terminalHistory, setTerminalHistory] = useState<Array<{type: 'command' | 'output' | 'banner' | 'error'; content: string}>>([
    {
      type: 'banner', 
      content: isRTL ? `
╭───────────────────────────────────────────────────╮
│                                                   │
│  مرحباً بك في سطر الاوامر الطيب                    │
│                                                   │
│  سطر الاوامر الإصدار 1.1.0                        │
│                                                   │
│  اكتب -م للحصول على المساعدة                      │
│                                                   │
╰───────────────────────────────────────────────────╯
` : `
╭───────────────────────────────────────────────────╮
│                                                   │
│  Welcome to Altayeb Command Line                  │
│                                                   │
│  Command Line Version 1.1.0                       │
│                                                   │
│  Type -h for help                                 │
│                                                   │
╰───────────────────────────────────────────────────╯
`
    },
    {type: 'command', content: isRTL ? 'من_أنا' : 'whoami'},
    {type: 'output', content: isRTL ? 'زائر@محفظة' : 'visitor@portfolio'},
  ]);
  
  const [terminalUser, setTerminalUser] = useState(isRTL ? 'زائر' : 'visitor');
  const terminalHost = isRTL ? 'محفظة' : 'portfolio';
  const [terminalPath, setTerminalPath] = useState(isRTL ? '/المنزل/الطيب' : '/home/<USER>');
  
  // Code editor states
  const [codeContent, setCodeContent] = useState([
    `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>My Web Project</title>
</head>
<body>
  <div class="container">
    <h1>Welcome to My Portfolio</h1>
    <p>Hello! I'm <span class="highlight">Altayeb</span>, a full-stack developer.</p>
    
    <div class="skills">
      <h2>My Skills</h2>
      <ul id="skills-list">
        <li>React & TypeScript</li>
        <li>UI/UX Design</li>
        <li>Prompt Engineering</li>
      </ul>
    </div>
    
    <button id="contact-btn">Contact Me</button>
  </div>
</body>
</html>`,

    `/* Main Styles */
body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #f8f9fa;
  background-color: #0f172a;
  margin: 0;
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 10px;
  background-color: #1e293b;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #38bdf8;
  margin-bottom: 15px;
}

.highlight {
  color: #38bdf8;
  font-weight: bold;
}

.skills {
  margin-top: 30px;
  padding: 15px;
  background-color: #334155;
  border-radius: 8px;
}

h2 {
  color: #a5f3fc;
  font-size: 1.4rem;
  margin-bottom: 10px;
}

ul {
  list-style-type: none;
  padding-left: 10px;
}

li {
  padding: 5px 0;
  position: relative;
}

li::before {
  content: "→";
  color: #38bdf8;
  margin-right: 10px;
}

button {
  background-color: #38bdf8;
  color: #0f172a;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 20px;
  transition: all 0.3s ease;
}

button:hover {
  background-color: #0ea5e9;
  transform: translateY(-2px);
}`,

    `// Interactive elements
document.addEventListener('DOMContentLoaded', () => {
  // Add animation to skills
  const skillsList = document.getElementById('skills-list');
  const skills = skillsList.getElementsByTagName('li');
  
  Array.from(skills).forEach((skill, index) => {
    skill.style.opacity = '0';
    skill.style.transform = 'translateX(-20px)';
    skill.style.transition = 'all 0.5s ease';
    
    setTimeout(() => {
      skill.style.opacity = '1';
      skill.style.transform = 'translateX(0)';
    }, 300 * (index + 1));
  });
  
  // Contact button effect
  const contactBtn = document.getElementById('contact-btn');
  
  contactBtn.addEventListener('click', () => {
    contactBtn.textContent = 'Email: <EMAIL>';
    contactBtn.style.backgroundColor = '#10b981';
  });
  
  // Add dark/light mode toggle
  const container = document.querySelector('.container');
  let darkMode = true;
  
  document.addEventListener('keydown', (e) => {
    if (e.key === 'T' || e.key === 't') {
      darkMode = !darkMode;
      
      if (darkMode) {
        document.body.style.backgroundColor = '#0f172a';
        container.style.backgroundColor = '#1e293b';
        document.body.style.color = '#f8f9fa';
      } else {
        document.body.style.backgroundColor = '#f8f9fa';
        container.style.backgroundColor = '#ffffff';
        document.body.style.color = '#1e293b';
      }
    }
  });
});`
  ]);

  // Arabic code content
  const arabicCodeContent = [
    `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>موقعي الشخصي</title>
</head>
<body>
  <div class="container">
    <h1>مرحباً بك في محفظتي</h1>
    <p>أهلاً! أنا <span class="highlight">الطيب</span>، مطور شامل.</p>
    
    <div class="skills">
      <h2>مهاراتي</h2>
      <ul id="skills-list">
        <li>رياكت و تايب سكريبت</li>
        <li>تصميم واجهة المستخدم</li>
        <li>هندسة النصوص التوجيهية</li>
      </ul>
    </div>
    
    <button id="contact-btn">تواصل معي</button>
  </div>
</body>
</html>`,

    `/* الأنماط الرئيسية */
body {
  font-family: 'Tajawal', sans-serif;
  line-height: 1.6;
  color: #f8f9fa;
  background-color: #0f172a;
  margin: 0;
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 10px;
  background-color: #1e293b;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #38bdf8;
  margin-bottom: 15px;
}

.highlight {
  color: #38bdf8;
  font-weight: bold;
}

.skills {
  margin-top: 30px;
  padding: 15px;
  background-color: #334155;
  border-radius: 8px;
}

h2 {
  color: #a5f3fc;
  font-size: 1.4rem;
  margin-bottom: 10px;
}

ul {
  list-style-type: none;
  padding-right: 10px;
}

li {
  padding: 5px 0;
  position: relative;
}

li::before {
  content: "←";
  color: #38bdf8;
  margin-left: 10px;
}

button {
  background-color: #38bdf8;
  color: #0f172a;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 20px;
  transition: all 0.3s ease;
}

button:hover {
  background-color: #0ea5e9;
  transform: translateY(-2px);
}`,

    `// العناصر التفاعلية
document.addEventListener('DOMContentLoaded', () => {
  // إضافة التأثير الحركي للمهارات
  const skillsList = document.getElementById('skills-list');
  const skills = skillsList.getElementsByTagName('li');
  
  Array.from(skills).forEach((skill, index) => {
    skill.style.opacity = '0';
    skill.style.transform = 'translateX(20px)';
    skill.style.transition = 'all 0.5s ease';
    
    setTimeout(() => {
      skill.style.opacity = '1';
      skill.style.transform = 'translateX(0)';
    }, 300 * (index + 1));
  });
  
  // تأثير زر الاتصال
  const contactBtn = document.getElementById('contact-btn');
  
  contactBtn.addEventListener('click', () => {
    contactBtn.textContent = 'البريد: <EMAIL>';
    contactBtn.style.backgroundColor = '#10b981';
  });
  
  // إضافة التبديل بين الوضع المظلم والفاتح
  const container = document.querySelector('.container');
  let darkMode = true;
  
  document.addEventListener('keydown', (e) => {
    if (e.key === 'ت' || e.key === 'T') {
      darkMode = !darkMode;
      
      if (darkMode) {
        document.body.style.backgroundColor = '#0f172a';
        container.style.backgroundColor = '#1e293b';
        document.body.style.color = '#f8f9fa';
      } else {
        document.body.style.backgroundColor = '#f8f9fa';
        container.style.backgroundColor = '#ffffff';
        document.body.style.color = '#1e293b';
      }
    }
  });
});`
  ];
  
  const filenames = ["index.html", "styles.css", "script.js"];
  const fileIcons = [<Globe className="w-3 h-3 text-orange-400" />, <FileText className="w-3 h-3 text-blue-400" />, <FileCode className="w-3 h-3 text-yellow-400" />];
  
  // For preview
  const [previewContent, setPreviewContent] = useState<React.ReactNode | null>(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewError, setPreviewError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  
  // Generate preview of HTML/CSS/JS code
  const generatePreview = () => {
    try {
      // Always use the complete code content for preview, not the animated displayText
      const html = codeContent[0];
      const css = codeContent[1];
      const js = codeContent[2];
      
      // Add CSS to hide scrollbars in the iframe content
      const noScrollbarCSS = `
        /* Hide scrollbars in preview */
        * {
          -ms-overflow-style: none !important;
          scrollbar-width: none !important;
        }
        
        *::-webkit-scrollbar {
          display: none !important;
        }
        
        body {
          overflow-x: hidden !important;
          overflow-y: auto !important;
        }
      `;
      
      // Create a complete HTML document with embedded CSS and JS
      const fullHtml = `
        ${html.replace('</head>', `<style>${css}\n${noScrollbarCSS}</style></head>`).replace('</body>', `<script>${js}</script></body>`)}
      `;
      
      return fullHtml;
    } catch (error) {
      setPreviewError((error as Error).message);
      return '<html><body><p>Error generating preview</p></body></html>';
    }
  };
  
  // Update iframe content
  const updatePreview = () => {
    try {
      setPreviewLoading(true);
      setPreviewError(null);
      
      const fullHtml = generatePreview();
      
      // Using srcdoc instead of contentWindow.document to avoid cross-origin issues
      if (iframeRef.current) {
        iframeRef.current.srcdoc = fullHtml;
      }
      
      setPreviewError(null);
    } catch (error) {
      setPreviewError((error as Error).message);
    } finally {
      setPreviewLoading(false);
    }
  };
  
  // Update preview when code changes or preview is toggled
  useEffect(() => {
    if (showPreview) {
      const timer = setTimeout(() => {
        updatePreview();
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [showPreview, codeContent]);
  
  // New code suggestions for auto-complete
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [cursorLine, setCursorLine] = useState(0);
  const [highlightLine, setHighlightLine] = useState(-1);
  
  // Terminal typing effect
  const [displayText, setDisplayText] = useState('');
  
  // Add file system simulation
  const [fileSystem, setFileSystem] = useState({
    '/المنزل/الطيب': {
      type: 'dir',
      content: {
        'documents': { type: 'dir' },
        'images': { type: 'dir' },
        'projects': { type: 'dir' },
        'resume.pdf': { 
          type: 'file',
          content: `# ALTAYEB
Full Stack Developer | Prompt Engineer | AI Specialist

## SUMMARY
Specialized in AI-enhanced applications, prompt engineering, and crafting exceptional 
e-commerce experiences with multilingual support including RTL languages. Bridging 
human creativity with AI capabilities to build innovative solutions.

## SKILLS
- Prompt Engineering & AI Integration
- Frontend: React, TypeScript, Tailwind CSS
- Backend: Node.js, Express, Firebase
- E-commerce Development
- Multilingual & RTL Support
- Responsive Web Design
- UI/UX Design
`
        },
        'about-me.md': { 
          type: 'file',
          content: `# About Me

I'm a Prompt Engineering specialist leveraging AI to solve complex problems and build efficient solutions, with expertise in modern web technologies.

Skilled in developing AI-enhanced applications using Claude AI and other large language models, I'm passionate about combining human creativity with AI capabilities.

I create responsive and elegant e-commerce platforms with multilingual support including RTL languages, and I'm an advocate for AI-assisted development to streamline workflows.

My focus is on building accessible applications that provide exceptional user experiences, using modern UI frameworks like Tailwind CSS and Shadcn UI components.
`
        },
        'contact.txt': { 
          type: 'file',
          content: `# Contact Information

Email: <EMAIL>
GitHub: github.com/altyb
LinkedIn: linkedin.com/in/altyb
Twitter: @altayeb_dev

Feel free to reach out for projects, collaborations, or just to say hello!
`
        }
      }
    },
    '/المنزل/الطيب/documents': {
      type: 'dir',
      content: {
        'skills.md': { 
          type: 'file',
          content: `# Technical Skills

## Frontend
- React: 95%
- TypeScript: 90%
- UI/UX Design: 85%

## Backend
- Node.js: 85%
- GraphQL: 75%

## Other
- Git: 90%
- Docker: 85%
`
        },
        'services.md': { 
          type: 'file',
          content: `# Services Offered

- AI-Enhanced Web Applications
- E-commerce Platform Development
- Prompt Engineering & LLM Integration
- Responsive Web Design
- Multilingual Website Development with RTL Support
- Performance Optimization
- UI/UX Design
`
        },
        'bio.txt': { 
          type: 'file',
          content: `I'm a Full Stack Developer specializing in AI-enhanced applications and modern web technologies.

Based in the Middle East with over 5 years of experience, I focus on creating elegant solutions that combine cutting-edge technology with exceptional user experiences.

My development philosophy centers on creating accessible, responsive, and performant applications that solve real problems for users.`
        }
      }
    },
    '/المنزل/الطيب/images': {
      type: 'dir',
      content: {
        'portfolio': { type: 'dir' },
        'profile.jpg': { 
          type: 'file', 
          content: '[Profile Image - A professional headshot]'
        },
        'banner.png': { 
          type: 'file',
          content: '[Portfolio Banner Image]'
        },
        'logo.svg': { 
          type: 'file',
          content: '[Personal Brand Logo]'
        }
      }
    },
    '/المنزل/الطيب/images/portfolio': {
      type: 'dir',
      content: {
        'watches-store.jpg': { 
          type: 'file',
          content: '[Luxury Watches E-commerce Project Screenshot]'
        },
        'vusto.jpg': { 
          type: 'file', 
          content: '[Vusto Restaurant Website Screenshot]'
        },
        'reelify.jpg': { 
          type: 'file', 
          content: '[Reelify Movie Platform Screenshot]'
        },
        'elegance.jpg': { 
          type: 'file',
          content: '[Elegance Arabic Fashion Store Screenshot]'
        },
        'flux.jpg': { 
          type: 'file',
          content: '[Flux Digital Services Marketplace Screenshot]'
        }
      }
    },
    '/المنزل/الطيب/projects': {
      type: 'dir',
      content: {
        'watches-store': { type: 'dir' },
        'vusto-restaurant': { type: 'dir' },
        'reelify': { type: 'dir' },
        'elegance': { type: 'dir' },
        'flux': { type: 'dir' },
        'README.md': { 
          type: 'file',
          content: `# My Projects

This directory contains my featured portfolio projects:

1. Watches Store - Luxury Timepieces E-commerce
2. Vusto - Fine Dining Restaurant
3. Reelify - Movie Exploration Platform
4. Elegance - Modern Arabic Fashion Platform
5. Flux Digital Canvas - Digital Services Marketplace

Each project folder contains source code and assets for the respective project.`
        }
      }
    },
    '/المنزل/الطيب/projects/watches-store': {
      type: 'dir',
      content: {
        'README.md': { 
          type: 'file',
          content: `# Watches Store | Luxury Timepieces

A modern, luxurious e-commerce platform specializing in high-end watches, built with React, TypeScript, and Shadcn UI components for an elegant shopping experience.

## Features
- Browse premium luxury watches with detailed product information
- Filter products by category, price range, and more
- Save favorite items to wishlist
- Fully functional shopping cart with quantity controls
- User profile management
- Responsive design for all devices
- Modern UI with Shadcn components

## Tech Stack
Built with React 18 and TypeScript using Vite. Features React Router v6, TailwindCSS with Shadcn UI, Context API for state management, TanStack React Query for data fetching, and React Hook Form with Zod validation. UI enhanced with Radix UI primitives, Embla Carousel, Framer Motion animations, and Sonner toasts.

## Live Demo
https://watches-store.netlify.app/`
        }
      }
    },
    '/المنزل/الطيب/projects/vusto-restaurant': {
      type: 'dir',
      content: {
        'README.md': { 
          type: 'file',
          content: `# Vusto - Fine Dining Restaurant

Elegant, modern restaurant website designed to showcase culinary excellence and provide an exceptional user experience with beautiful, responsive design.

## Features
- Stunning visual design with parallax effects
- Responsive layout for all devices
- Interactive menu with beautiful presentation
- Reservation system with contact form
- Testimonials and customer reviews
- Subtle scroll reveal animations

## Tech Stack
Built with React and TypeScript, using Vite for development. Features shadcn/ui components, Tailwind CSS for styling, Framer Motion for animations, and React Router for navigation.

## Live Demo
https://vusto.netlify.app/

## Source Code
https://github.com/altyb/Vusto`
        }
      }
    },
    '/المنزل/الطيب/projects/flux': {
      type: 'dir',
      content: {
        'README.md': { 
          type: 'file',
          content: `# Flux Digital Canvas - Digital Services Marketplace

Sophisticated, responsive e-commerce platform specializing in digital services with multi-language support, dynamic theme customization, and a robust shopping cart system.

## Features
- Responsive design optimized for all devices
- Multilingual support with RTL capabilities
- Theme customization with light/dark modes
- Interactive UI with 3D animations
- E-commerce functionality with persistent cart
- Admin dashboard with analytics

## Tech Stack
Built with React and TypeScript using Vite. Features Tailwind CSS and Shadcn UI for styling, Framer Motion for animations, and Context API for state management. Optimized with code splitting and lazy loading.

## Live Demo
https://digital-mart.netlify.app/`
        }
      }
    }
  });

  // Syntax highlighting function for HTML/CSS/JS
  const highlightSyntax = (code: string) => {
    // Different highlighting based on file type
    if (activeTab === 0) { // HTML
      return code
        .replace(/(&lt;[\/]?)([\w\-]+)([^&]*?)(&gt;)/g, '$1<span class="text-purple-400">$2</span>$3$4')  // tags
        .replace(/(&lt;[\/]?)([\w\-]+)/g, '$1<span class="text-purple-400">$2</span>')  // opening tags
        .replace(/(\w+)="([^"]*)"/g, '<span class="text-yellow-300">$1</span>="<span class="text-green-300">$2</span>"') // attributes
        .replace(/(&gt;)([^&lt;]*)(&lt;)/g, '$1<span class="text-gray-300">$2</span>$3')  // content
        .replace(/&lt;!DOCTYPE html&gt;/g, '<span class="text-gray-500">&lt;!DOCTYPE html&gt;</span>'); // doctype
    } else if (activeTab === 1) { // CSS
      return code
        .replace(/([\.\#]?[\w\-]+)(\s*\{)/g, '<span class="text-purple-400">$1</span>$2') // selectors
        .replace(/(\{)([^\}]*)(\})/g, '$1<span class="text-gray-300">$2</span>$3')  // rule content
        .replace(/([\w\-]+)(\s*:)/g, '<span class="text-blue-300">$1</span>$2') // property names
        .replace(/(:)([^;]*)([;\n])/g, '$1<span class="text-green-300">$2</span>$3'); // property values
    } else { // JavaScript
      return code
        .replace(/\b(const|let|var|function|return|document|window|if|else|for|while|try|catch|addEventListener)\b/g, '<span class="text-purple-400">$1</span>')  // keywords
        .replace(/('.*?'|".*?")/g, '<span class="text-green-300">$1</span>') // strings
        .replace(/\b(true|false|null|undefined)\b/g, '<span class="text-yellow-500">$1</span>') // literals
        .replace(/\b(\d+)\b/g, '<span class="text-blue-400">$1</span>') // numbers
        .replace(/\/\/.*/g, '<span class="text-gray-500">$&</span>'); // comments
    }
  };

  // Function to get directory content
  const getDirectoryContent = (path: string) => {
    if (fileSystem[path] && fileSystem[path].type === 'dir') {
      const content = fileSystem[path].content;
      let result = '';
      
      // Group directories and files
      const dirs = Object.keys(content).filter(item => content[item].type === 'dir');
      const files = Object.keys(content).filter(item => content[item].type === 'file');
      
      // Format output
      dirs.forEach(dir => {
        result += `📁 ${dir}/  `;
      });
      
      files.forEach(file => {
        result += `📄 ${file}  `;
      });
      
      return result;
    }
    return 'Directory not found';
  };

  // Function to view file content
  const getFileContent = (path: string) => {
    const parts = path.split('/');
    const fileName = parts[parts.length - 1];
    const dirPath = parts.slice(0, -1).join('/');
    
    if (fileSystem[dirPath] && 
        fileSystem[dirPath].type === 'dir' && 
        fileSystem[dirPath].content[fileName] && 
        fileSystem[dirPath].content[fileName].type === 'file') {
      return fileSystem[dirPath].content[fileName].content || 'No content available';
    }
    return 'File not found';
  };
  
  useEffect(() => {
    let index = 0;
    const currentText = codeContent[activeTab];
    const timer = setInterval(() => {
      setDisplayText(currentText.substring(0, index));
      index++;
      if (index > currentText.length) clearInterval(timer);
    }, 25);
    
    return () => clearInterval(timer);
  }, [activeTab, codeContent]);
  
  // Mouse follower effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (heroRef.current) {
        const { left, top, width, height } = heroRef.current.getBoundingClientRect();
        const x = (e.clientX - left) / width;
        const y = (e.clientY - top) / height;
        setCursorPosition({ x, y });
      }
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleCopyCode = () => {
    navigator.clipboard.writeText(codeContent[activeTab]);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleTabChange = (index: number) => {
    setActiveTab(index);
    setDisplayText('');
    setIsEditing(false);
  };

  const handleCodeEdit = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newCodeContent = [...codeContent];
    newCodeContent[activeTab] = e.target.value;
    setCodeContent(newCodeContent);
  };

  const clearTerminal = () => {
    setTerminalHistory([{
      type: 'banner', 
      content: isRTL ? `
╭───────────────────────────────────────────────────╮
│                                                   │
│  مرحباً بك في سطر الاوامر الطيب                    │
│                                                   │
│  سطر الاوامر الإصدار 1.1.0                        │
│                                                   │
│  اكتب -م للحصول على المساعدة                      │
│                                                   │
╰───────────────────────────────────────────────────╯
` : `
╭───────────────────────────────────────────────────╮
│                                                   │
│  Welcome to Altayeb Command Line                  │
│                                                   │
│  Command Line Version 1.1.0                       │
│                                                   │
│  Type -h for help                                 │
│                                                   │
╰───────────────────────────────────────────────────╯
`
    }]);
  };

  const handleTerminalCommand = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const command = terminalCommand.trim();
      
      // Add the command to history with the prompt
      const promptText = `${terminalUser}@${terminalHost}:${terminalPath}$`;
      let updatedHistory = [...terminalHistory, {type: 'command' as const, content: `${promptText} ${command}`}];
      
      // Process different commands
      if (command === '') {
        // Empty command, just add a new prompt line
        updatedHistory.push({type: 'output' as const, content: ''});
      } else if (command === 'clear' || command === 'c' || command === 'مسح' || command === 'م') {
        // Clear the terminal
        clearTerminal();
        setTerminalCommand('');
        return;
      } else if (command === 'help' || command === '-h' || command === 'مساعدة' || command === '-م') {
        // Auto-clear for better UX
        clearTerminal();
        updatedHistory = [{type: 'banner' as const, content: updatedHistory[0].content}];
        
        if (isRTL) {
          updatedHistory.push({type: 'output' as const, content: `
الأوامر المتاحة:
╭───────────────────────────────────────────────────────────────╮
│ مساعدة [-م]    │ مهارات [-م]   │ من_أنا [-ن]    │ تاريخ       │
│ مسح [م]       │ مشاريع [-ش]   │ خروج [خ]       │ نظام        │
│ نبذة [-ن]     │ تواصل [-ت]    │ مسار           │ دخول [اسم]  │
│ عرض          │ انتقل [دليل]   │ صدى [نص]      │ مشاهدة [ملف] │
╰───────────────────────────────────────────────────────────────╯`});
        } else {
          updatedHistory.push({type: 'output' as const, content: `
Available commands:
╭───────────────────────────────────────────────────────────────╮
│ help [-h]     │ skills [-s]   │ whoami         │ date         │
│ clear [c]     │ projects [-p] │ exit [x]       │ system       │
│ about [-a]    │ contact [-c]  │ pwd            │ login [name] │
│ ls            │ cd [dir]      │ echo [text]    │ cat [file]   │
╰───────────────────────────────────────────────────────────────╯`});
        }
      } else if (command === 'about' || command === '-a' || command === 'نبذة' || command === '-ن') {
        clearTerminal();
        updatedHistory = [{type: 'banner' as const, content: updatedHistory[0].content}];
        if (isRTL) {
          updatedHistory.push({type: 'output' as const, content: `
╭────────────────────────────────╮
│ الطيب :: مطور ومصمم            │
╰────────────────────────────────╯

أنا مطور شغوف متخصص في 
إنشاء تجارب ويب أنيقة تركز على المستخدم.

مقيم في: الشرق الأوسط
الخبرة: أكثر من 5 سنوات
التركيز: رياكت، تايب سكريبت، تصميم واجهة المستخدم`});
        } else {
          updatedHistory.push({type: 'output' as const, content: `
╭────────────────────────────────╮
│ Altayeb :: Developer & Designer │
╰────────────────────────────────╯

I'm a passionate developer specializing in
creating elegant, user-focused web experiences.

Location: Middle East
Experience: 5+ years
Focus: React, TypeScript, UI/UX Design`});
        }
      } else if (command === 'skills' || command === '-s' || command === 'مهارات' || command === '-ه') {
        clearTerminal();
        updatedHistory = [{type: 'banner' as const, content: updatedHistory[0].content}];
        if (isRTL) {
          updatedHistory.push({type: 'output' as const, content: `
╭─────── المهارات التقنية ───────────╮
│ الواجهة الأمامية                   │
│ رياكت         ██████████████▒▒ 95% │
│ تايب سكريبت   █████████████▒▒▒ 90% │
│ واجهة المستخدم████████████▒▒▒▒ 85% │
│                                    │
│ الخلفية                            │
│ نود.جي.إس     ████████████▒▒▒▒ 85% │
│ جرافكيو.إل    ██████████▒▒▒▒▒▒ 75% │
│                                    │
│ أخرى                               │
│ جيت           █████████████▒▒▒ 90% │
│ دوكر          ████████████▒▒▒▒ 85% │
╰────────────────────────────────────╯`});
        } else {
          updatedHistory.push({type: 'output' as const, content: `
╭─────── Technical Skills ───────────╮
│ Frontend                           │
│ React         ██████████████▒▒ 95% │
│ TypeScript    █████████████▒▒▒ 90% │
│ UI/UX Design  ████████████▒▒▒▒ 85% │
│                                    │
│ Backend                            │
│ Node.js       ████████████▒▒▒▒ 85% │
│ GraphQL       ██████████▒▒▒▒▒▒ 75% │
│                                    │
│ Other                              │
│ Git           █████████████▒▒▒ 90% │
│ Docker        ████████████▒▒▒▒ 85% │
╰────────────────────────────────────╯`});
        }
      } else if (command === 'projects' || command === '-p' || command === 'مشاريع' || command === '-ش') {
        clearTerminal();
        updatedHistory = [{type: 'banner' as const, content: updatedHistory[0].content}];
        if (isRTL) {
          updatedHistory.push({type: 'output' as const, content: `
╭─────── المشاريع ───────────────────────╮
│ 1. موقع المحفظة                        │
│    رياكت • تيلويند سي.إس.إس • فريمر موشن │
│                                         │
│ 2. منصة تجارة إلكترونية                 │
│    نكست.جي.إس • جرافكيو.إل • سترايب      │
│                                         │
│ 3. مولد محتوى ذكاء اصطناعي              │
│    رياكت • تايب سكريبت • أوبن أي.آي      │
│                                         │
│ 4. لوحة تحليلات                         │
│    رياكت • D3.js • فايربيس              │
│                                         │
│ 5. تطبيق لياقة بدنية للجوال             │
│    رياكت نيتف • تايب سكريبت             │
╰─────────────────────────────────────────╯`});
        } else {
          updatedHistory.push({type: 'output' as const, content: `
╭─────── Projects ───────────────────────╮
│ 1. Portfolio Website                   │
│    React • Tailwind CSS • Framer Motion │
│                                         │
│ 2. E-Commerce Platform                  │
│    Next.js • GraphQL • Stripe           │
│                                         │
│ 3. AI Content Generator                 │
│    React • TypeScript • OpenAI          │
│                                         │
│ 4. Analytics Dashboard                  │
│    React • D3.js • Firebase             │
│                                         │
│ 5. Mobile Fitness App                   │
│    React Native • TypeScript            │
╰─────────────────────────────────────────╯`});
        }
      } else if (command === 'contact' || command === '-c' || command === 'تواصل' || command === '-ت') {
        clearTerminal();
        updatedHistory = [{type: 'banner' as const, content: updatedHistory[0].content}];
        if (isRTL) {
          updatedHistory.push({type: 'output' as const, content: `
╭─────── معلومات التواصل ───────────╮
│ البريد: <EMAIL>       │
│ جيتهاب: github.com/altyb           │
│ لينكد إن: linkedin.com/in/altyb    │
│ تويتر: @altayeb_dev                │
╰─────────────────────────────────────╯`});
        } else {
          updatedHistory.push({type: 'output' as const, content: `
╭─────── Contact Information ───────────╮
│ Email: <EMAIL>           │
│ GitHub: github.com/altyb             │
│ LinkedIn: linkedin.com/in/altyb      │
│ Twitter: @altayeb_dev                │
╰─────────────────────────────────────────╯`});
        }
      } else if (command === 'whoami' || command === 'من_أنا' || command === '-ن') {
        updatedHistory.push({type: 'output' as const, content: terminalUser});
      } else if (command === 'exit' || command === 'x' || command === 'خروج' || command === 'خ') {
        setActiveView('code');
        setTerminalCommand('');
        return;
      } else if (command === 'ls' || command === 'عرض') {
        const directoryContent = getDirectoryContent(terminalPath);
        updatedHistory.push({type: 'output' as const, content: `\n${directoryContent}\n`});
      } else if (command === 'pwd' || command === 'مسار') {
        updatedHistory.push({type: 'output' as const, content: terminalPath});
      } else if (command.startsWith('cd ') || command.startsWith('انتقل ')) {
        const dir = command.startsWith('cd ') ? command.split(' ')[1] : command.split(' ')[1];
        // Update the path for a more realistic feel
        if (dir === '..') {
          const parts = terminalPath.split('/');
          if (parts.length > 2) { // Don't go above root
            parts.pop();
            const newPath = parts.join('/');
            setTerminalPath(newPath);
            updatedHistory.push({type: 'output' as const, content: isRTL ? `تم الانتقال إلى الدليل ${newPath}` : `Changed directory to ${newPath}`});
          } else {
            updatedHistory.push({type: 'output' as const, content: isRTL ? `أنت بالفعل في الدليل الرئيسي` : `Already at root directory`});
          }
        } else if (dir.startsWith('/')) {
          // Absolute path
          if (fileSystem[dir]) {
            setTerminalPath(dir);
            updatedHistory.push({type: 'output' as const, content: isRTL ? `تم الانتقال إلى الدليل ${dir}` : `Changed directory to ${dir}`});
          } else {
            updatedHistory.push({type: 'error' as const, content: isRTL ? `الدليل غير موجود: ${dir}` : `Directory not found: ${dir}`});
          }
        } else {
          // Relative path
          const newPath = `${terminalPath}/${dir}`;
          if (fileSystem[newPath]) {
            setTerminalPath(newPath);
            updatedHistory.push({type: 'output' as const, content: isRTL ? `تم الانتقال إلى الدليل ${newPath}` : `Changed directory to ${newPath}`});
          } else {
            updatedHistory.push({type: 'error' as const, content: isRTL ? `الدليل غير موجود: ${dir}` : `Directory not found: ${dir}`});
          }
        }
      } else if (command.startsWith('cat ') || command.startsWith('مشاهدة ')) {
        const fileName = command.startsWith('cat ') ? command.split(' ')[1] : command.split(' ')[1];
        let filePath;
        
        // Handle absolute or relative paths
        if (fileName.startsWith('/')) {
          filePath = fileName;
        } else {
          filePath = `${terminalPath}/${fileName}`;
        }
        
        const fileContent = getFileContent(filePath);
        updatedHistory.push({type: 'output' as const, content: `\n${fileContent}\n`});
      } else if (command.startsWith('echo ') || command.startsWith('صدى ')) {
        const text = command.startsWith('echo ') ? command.substring(5) : command.substring(5);
        updatedHistory.push({type: 'output' as const, content: text});
      } else if (command === 'date' || command === 'تاريخ') {
        updatedHistory.push({type: 'output' as const, content: isRTL ? new Date().toLocaleString('ar-SA') : new Date().toLocaleString('en-US')});
      } else if (command === 'system' || command === 'نظام' || command === 'uname') {
        updatedHistory.push({type: 'output' as const, content: isRTL ? 'نظام المحفظة الإصدار 1.1.0 بناء-2023' : 'Portfolio System Version 1.1.0 Build-2023'});
      } else if (command.startsWith('login ') || command.startsWith('دخول ')) {
        const username = command.startsWith('login ') ? command.split(' ')[1] : command.split(' ')[1];
        setTerminalUser(username);
        updatedHistory.push({type: 'output' as const, content: isRTL ? `تم تسجيل الدخول باسم ${username}` : `Logged in as ${username}`});
      } else {
        // Command not found
        updatedHistory.push({type: 'error' as const, content: isRTL 
          ? `الأمر غير موجود: ${command}. اكتب '-م' للمساعدة.` 
          : `Command not found: ${command}. Type '-h' for help.`});
      }
      
      // Update history and reset command
      setTerminalHistory(updatedHistory);
      setTerminalCommand('');
    }
  };

  const handleSwitchView = () => {
    setActiveView(activeView === 'code' ? 'terminal' : 'code');
  };

  // Handle textarea keydown for indentation and navigation
  const handleTextareaKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const textarea = e.currentTarget;
    const { selectionStart, selectionEnd } = textarea;
    
    // Get current line and its indentation
    const lines = textarea.value.split('\n');
    const currentLine = textarea.value.substring(0, selectionStart).split('\n').length - 1;
    setCursorLine(currentLine);
    
    // Auto indentation for braces
    if (e.key === 'Enter') {
      e.preventDefault();
      
      const currentLineText = lines[currentLine];
      const indentation = currentLineText.match(/^\s*/)?.[0] || '';
      let additionalIndent = '';
      
      // Add extra indentation after opening braces
      if (currentLineText.trim().endsWith('{') || currentLineText.trim().endsWith('(') || currentLineText.trim().endsWith('[')) {
        additionalIndent = '  ';
      }
      
      // Insert newline with proper indentation
      const newValue = 
        textarea.value.substring(0, selectionStart) + 
        '\n' + indentation + additionalIndent + 
        textarea.value.substring(selectionEnd);
      
      const newPosition = selectionStart + 1 + indentation.length + additionalIndent.length;
      
      const newCodeContent = [...codeContent];
      newCodeContent[activeTab] = newValue;
      setCodeContent(newCodeContent);
      
      // Set cursor position after update
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = newPosition;
      }, 0);
    }
    
    // Tab key for indentation
    else if (e.key === 'Tab') {
      e.preventDefault();
      const indentation = '  ';
      
      if (selectionStart === selectionEnd) {
        // No selection, just insert at cursor
        const newValue = 
          textarea.value.substring(0, selectionStart) + 
          indentation + 
          textarea.value.substring(selectionEnd);
        
        const newPosition = selectionStart + indentation.length;
        
        const newCodeContent = [...codeContent];
        newCodeContent[activeTab] = newValue;
        setCodeContent(newCodeContent);
        
        setTimeout(() => {
          textarea.selectionStart = textarea.selectionEnd = newPosition;
        }, 0);
      } else {
        // With selection, indent the entire selection
        const selectedText = textarea.value.substring(selectionStart, selectionEnd);
        const selectedLines = selectedText.split('\n');
        
        if (e.shiftKey) {
          // Unindent
          const processedLines = selectedLines.map(line => 
            line.startsWith('  ') ? line.substring(2) : line
          );
          
          const newText = processedLines.join('\n');
          const newValue = 
            textarea.value.substring(0, selectionStart) + 
            newText + 
            textarea.value.substring(selectionEnd);
          
          const newCodeContent = [...codeContent];
          newCodeContent[activeTab] = newValue;
          setCodeContent(newCodeContent);
          
          setTimeout(() => {
            textarea.selectionStart = selectionStart;
            textarea.selectionEnd = selectionStart + newText.length;
          }, 0);
        } else {
          // Indent
          const processedLines = selectedLines.map(line => indentation + line);
          
          const newText = processedLines.join('\n');
          const newValue = 
            textarea.value.substring(0, selectionStart) + 
            newText + 
            textarea.value.substring(selectionEnd);
          
          const newCodeContent = [...codeContent];
          newCodeContent[activeTab] = newValue;
          setCodeContent(newCodeContent);
          
          setTimeout(() => {
            textarea.selectionStart = selectionStart;
            textarea.selectionEnd = selectionStart + newText.length;
          }, 0);
        }
      }
    }
  };

  // Handle text selection and cursor position updates
  const handleTextareaSelect = (e: React.SyntheticEvent<HTMLTextAreaElement>) => {
    const textarea = e.currentTarget;
    const { selectionStart } = textarea;
    
    // Get current line
    const currentLine = textarea.value.substring(0, selectionStart).split('\n').length - 1;
    setCursorLine(currentLine);
    setHighlightLine(currentLine);
  };

  // Auto-suggestion handler
  const handleTextareaInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleCodeEdit(e);
    
    const textarea = e.currentTarget;
    const cursorPos = textarea.selectionStart;
    const text = textarea.value.substring(0, cursorPos);
    const lastWord = text.split(/\s/).pop() || '';
    
    // Show suggestions based on last word
    if (lastWord && lastWord.length > 1) {
      const codeKeywords = ['function', 'return', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'async', 'await', 'try', 'catch', 'import', 'export'];
      const filteredSuggestions = codeKeywords.filter(word => word.startsWith(lastWord));
      
      setSuggestions(filteredSuggestions);
      setShowSuggestions(filteredSuggestions.length > 0);
    } else {
      setShowSuggestions(false);
    }
  };

  // Apply a suggestion
  const applySuggestion = (suggestion: string) => {
    const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
    if (!textarea) return;
    
    const cursorPos = textarea.selectionStart;
    const text = textarea.value.substring(0, cursorPos);
    const lastWord = text.split(/\s/).pop() || '';
    
    // Replace the last word with the suggestion
    const beforeLastWord = text.substring(0, text.length - lastWord.length);
    const after = textarea.value.substring(cursorPos);
    
    const newValue = beforeLastWord + suggestion + after;
    
    const newCodeContent = [...codeContent];
    newCodeContent[activeTab] = newValue;
    setCodeContent(newCodeContent);
    
    setTimeout(() => {
      textarea.selectionStart = textarea.selectionEnd = beforeLastWord.length + suggestion.length;
      textarea.focus();
    }, 0);
    
    setShowSuggestions(false);
  };

  const handleShowPreview = () => {
    // If toggling preview on, immediately set to edit mode to show full code
    if (!showPreview) {
      setIsEditing(true);
    }
    setShowPreview(!showPreview);
  };

  // Handle export of files as ZIP
  const handleExportFiles = () => {
    // This function uses the JSZip library which should be imported in your project
    // If JSZip is not available, we'll show an alert to install it
    if (typeof window !== 'undefined') {
      // Check if JSZip is available globally (this could be added via CDN or npm)
      if ('JSZip' in window) {
        // @ts-ignore - JSZip is loaded globally
        const zip = new window.JSZip();
        
        // Add all files to the zip
        zip.file("index.html", codeContent[0]);
        zip.file("styles.css", codeContent[1]);
        zip.file("script.js", codeContent[2]);
        
        // Generate the zip file and trigger download
        zip.generateAsync({ type: "blob" })
          .then(function(content) {
            // Create a download link and trigger it
            const link = document.createElement('a');
            link.href = URL.createObjectURL(content);
            link.download = "my-web-project.zip";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          });
      } else {
        // Alert if JSZip is not available
        alert("JSZip library is not available. To enable export functionality, please add JSZip to your project.");
        console.log("To add JSZip, you can add this script to your HTML: <script src=\"https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js\"></script>");
      }
    }
  };

  // Function to create a downloadable file
  const downloadSingleFile = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Add simple JSZip implementation via CDN if it doesn't exist
  useEffect(() => {
    if (typeof window !== 'undefined' && !('JSZip' in window)) {
      const script = document.createElement('script');
      script.src = "https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js";
      script.async = true;
      document.body.appendChild(script);
    }
  }, []);

  // Update code content when language changes
  useEffect(() => {
    if (i18n.language === 'ar') {
      setCodeContent(arabicCodeContent);
    } else {
      setCodeContent([
        `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>My Web Project</title>
</head>
<body>
  <div class="container">
    <h1>Welcome to My Portfolio</h1>
    <p>Hello! I'm <span class="highlight">Altayeb</span>, a full-stack developer.</p>
    
    <div class="skills">
      <h2>My Skills</h2>
      <ul id="skills-list">
        <li>React & TypeScript</li>
        <li>UI/UX Design</li>
        <li>Prompt Engineering</li>
      </ul>
    </div>
    
    <button id="contact-btn">Contact Me</button>
  </div>
</body>
</html>`,

        `/* Main Styles */
body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #f8f9fa;
  background-color: #0f172a;
  margin: 0;
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 10px;
  background-color: #1e293b;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #38bdf8;
  margin-bottom: 15px;
}

.highlight {
  color: #38bdf8;
  font-weight: bold;
}

.skills {
  margin-top: 30px;
  padding: 15px;
  background-color: #334155;
  border-radius: 8px;
}

h2 {
  color: #a5f3fc;
  font-size: 1.4rem;
  margin-bottom: 10px;
}

ul {
  list-style-type: none;
  padding-left: 10px;
}

li {
  padding: 5px 0;
  position: relative;
}

li::before {
  content: "→";
  color: #38bdf8;
  margin-right: 10px;
}

button {
  background-color: #38bdf8;
  color: #0f172a;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 20px;
  transition: all 0.3s ease;
}

button:hover {
  background-color: #0ea5e9;
  transform: translateY(-2px);
}`,

        `// Interactive elements
document.addEventListener('DOMContentLoaded', () => {
  // Add animation to skills
  const skillsList = document.getElementById('skills-list');
  const skills = skillsList.getElementsByTagName('li');
  
  Array.from(skills).forEach((skill, index) => {
    skill.style.opacity = '0';
    skill.style.transform = 'translateX(-20px)';
    skill.style.transition = 'all 0.5s ease';
    
    setTimeout(() => {
      skill.style.opacity = '1';
      skill.style.transform = 'translateX(0)';
    }, 300 * (index + 1));
  });
  
  // Contact button effect
  const contactBtn = document.getElementById('contact-btn');
  
  contactBtn.addEventListener('click', () => {
    contactBtn.textContent = 'Email: <EMAIL>';
    contactBtn.style.backgroundColor = '#10b981';
  });
  
  // Add dark/light mode toggle
  const container = document.querySelector('.container');
  let darkMode = true;
  
  document.addEventListener('keydown', (e) => {
    if (e.key === 'T' || e.key === 't') {
      darkMode = !darkMode;
      
      if (darkMode) {
        document.body.style.backgroundColor = '#0f172a';
        container.style.backgroundColor = '#1e293b';
        document.body.style.color = '#f8f9fa';
      } else {
        document.body.style.backgroundColor = '#f8f9fa';
        container.style.backgroundColor = '#ffffff';
        document.body.style.color = '#1e293b';
      }
    }
  });
});`
      ]);
    }
  }, [i18n.language]);

  // Reinitialize terminal when language changes
  useEffect(() => {
    // Update terminal banner and initial state when language changes
    setTerminalHistory([
      {
        type: 'banner', 
        content: isRTL ? `
╭───────────────────────────────────────────────────╮
│                                                   │
│  مرحباً بك في سطر الاوامر الطيب                    │
│                                                   │
│  سطر الاوامر الإصدار 1.1.0                        │
│                                                   │
│  اكتب -م للحصول على المساعدة                      │
│                                                   │
╰───────────────────────────────────────────────────╯
` : `
╭───────────────────────────────────────────────────╮
│                                                   │
│  Welcome to Altayeb Command Line                  │
│                                                   │
│  Command Line Version 1.1.0                       │
│                                                   │
│  Type -h for help                                 │
│                                                   │
╰───────────────────────────────────────────────────╯
`
      },
      {type: 'command', content: isRTL ? 'من_أنا' : 'whoami'},
      {type: 'output', content: isRTL ? 'زائر@محفظة' : 'visitor@portfolio'},
    ]);
    
    setTerminalUser(isRTL ? 'زائر' : 'visitor');
    setTerminalPath(isRTL ? '/المنزل/الطيب' : '/home/<USER>');
  }, [i18n.language, isRTL]);

  return (
    <section 
      ref={heroRef}
      id="home" 
      className="relative min-h-screen flex items-center overflow-hidden"
      style={{
        background: `radial-gradient(
          circle at ${cursorPosition.x * 100}% ${cursorPosition.y * 100}%, 
          rgba(0, 255, 247, 0.15), 
          transparent 30%
        )`
      }}
    >
      {/* Apply scrollbar hiding styles globally within the component */}
      <style>{heroSectionStyle}</style>
      
      {/* Backdrop shapes */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 right-[20%] w-72 h-72 bg-neon/5 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-20 left-[10%] w-60 h-60 bg-neon/10 rounded-full blur-3xl animate-pulse-slow"></div>
        
        {/* Grid lines */}
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        
        {/* Code blocks floating */}
          <motion.div
          className="absolute top-[15%] left-[10%] p-4 bg-dark/60 backdrop-blur-md border border-white/10 rounded-lg opacity-20 hidden md:block"
          animate={{ 
            y: [0, 15, 0],
            rotate: [-1, 1, -1],
          }}
          transition={{ 
            duration: 6,
            repeat: Infinity,
            repeatType: "reverse" 
          }}
        >
          <Code className="h-8 w-8 text-neon" />
            </motion.div>

        <motion.div 
          className="absolute bottom-[15%] right-[10%] p-4 bg-dark/60 backdrop-blur-md border border-white/10 rounded-lg opacity-20 hidden md:block"
          animate={{ 
            y: [0, -15, 0],
            rotate: [1, -1, 1],
          }}
          transition={{ 
            duration: 5,
            repeat: Infinity,
            repeatType: "reverse",
            delay: 1
          }}
        >
          <Monitor className="h-8 w-8 text-neon" />
        </motion.div>

        <motion.div 
          className="absolute top-[40%] right-[5%] p-4 bg-dark/60 backdrop-blur-md border border-white/10 rounded-lg opacity-20 hidden md:block"
          animate={{ 
            y: [0, 20, 0],
            rotate: [1, -2, 1],
          }}
          transition={{ 
            duration: 7,
            repeat: Infinity,
            repeatType: "reverse",
            delay: 0.5
          }}
        >
          <Database className="h-8 w-8 text-neon" />
        </motion.div>
      </div>

      <div className="container relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-xl"
            >
              <h2 className={`text-lg font-mono text-neon mb-3 ${isRTL ? 'text-right' : 'text-left'}`}>
                <span className="inline-block w-12 h-[1px] bg-neon mr-4 align-middle"></span>
                {t('hero.title')}
              </h2>
              
              <h1 className={`text-5xl sm:text-6xl md:text-7xl font-bold leading-tight tracking-tight mb-6 ${getFontClass()} ${isRTL ? 'text-right' : 'text-left'}`}>
                <span className="text-gradient">{t('hero.name')}</span>
                <span className="block text-white mt-2">{t('hero.role')}</span>
              </h1>

              <p className={`text-gray-300 text-lg mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('hero.description')}
              </p>

              <div className={`flex flex-wrap gap-4 mb-8 ${isRTL ? 'justify-end' : 'justify-start'}`}>
              <ParticleButton 
                  className="bg-transparent text-white font-medium px-6 py-6 h-auto relative overflow-hidden group border border-neon hover:shadow-[0_0_15px_rgba(0,255,247,0.5)] hover:-translate-y-[2px] hover:rotate-[0.5deg] transition-all duration-300"
                asChild
                successDuration={800}
              >
                <Link to="/projects">
                  <span className="relative z-10 group-hover:text-dark group-hover:font-semibold transition-all duration-300">{t('hero.viewProjects')}</span>
                  <span className="absolute top-0 left-0 w-0 h-full bg-neon transform -skew-x-12 group-hover:w-[120%] transition-all duration-500 z-0"></span>
                  <ArrowRight className="ml-2 h-4 w-4 inline-block group-hover:text-dark group-hover:translate-x-1 group-hover:scale-125 transition-all duration-300 z-10" />
                </Link>
              </ParticleButton>
              <ParticleButton 
                variant="outline" 
                className="border-white/20 hover:border-neon px-6 py-6 h-auto relative overflow-hidden group hover:shadow-[0_0_15px_rgba(0,255,247,0.5)] hover:-translate-y-[2px] hover:rotate-[-0.5deg] transition-all duration-300"
                asChild
                successDuration={800}
              >
                <Link to="/contact">
                  <span className="relative z-10 font-medium group-hover:font-semibold group-hover:text-dark transition-all duration-300">{t('hero.contactMe')}</span>
                  <span className="absolute inset-0 bg-neon/0 group-hover:bg-neon transition-all duration-300 z-0"></span>
                  <span className="absolute bottom-0 left-0 h-0 w-full bg-neon/30 group-hover:h-[6px] transition-all duration-500 ease-in-out z-0"></span>
                  <ArrowRight className="ml-2 h-4 w-4 inline-block translate-x-0 opacity-0 group-hover:opacity-100 group-hover:text-dark group-hover:translate-x-1 transition-all duration-300 z-10" />
                </Link>
              </ParticleButton>
              </div>

              <div className={`flex gap-4 ${isRTL ? 'justify-end' : 'justify-start'}`}>
              <a 
                href="https://github.com/altyb" 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-2 rounded-full border border-white/20 hover:border-neon hover:text-neon transition-colors"
                  aria-label="GitHub"
              >
                <Github className="h-5 w-5" />
              </a>
              <a 
                href="https://www.linkedin.com/in/altyb" 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-2 rounded-full border border-white/20 hover:border-neon hover:text-neon transition-colors"
                  aria-label="LinkedIn"
              >
                <Linkedin className="h-5 w-5" />
              </a>
              </div>
            </motion.div>
          </div>

          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.3 }}
            className="relative hidden lg:block"
          >
            {/* Perspective Container for 3D Effect */}
            <div className="perspective-container">
              {/* Window Tab Switcher - Updated to stacked cards */}
              <div className="relative flex justify-center mb-4 h-12">
                <div 
                  onClick={() => setActiveView('terminal')}
                  data-active={activeView === 'terminal' ? "true" : "false"}
                  className={`absolute window-tab px-4 py-2 rounded-lg font-mono text-sm flex items-center gap-2 cursor-pointer transition-all duration-300 ${
                    activeView === 'code' 
                      ? 'right-1/2 translate-x-[45%] rotate-[-5deg] z-0 bg-gray-900/80 text-gray-400 hover:text-white transform-gpu'
                      : 'right-1/2 translate-x-1/2 z-10 bg-gray-800 text-neon border border-neon/30 transform-gpu'
                  }`}
                >
                  <Terminal className="h-4 w-4" />
                  {isRTL ? 'سطر الاوامر' : 'Terminal'}
                </div>
                <div 
                  onClick={() => setActiveView('code')}
                  data-active={activeView === 'code' ? "true" : "false"}
                  className={`absolute window-tab px-4 py-2 rounded-lg font-mono text-sm flex items-center gap-2 cursor-pointer transition-all duration-300 ${
                    activeView === 'code' 
                      ? 'left-1/2 -translate-x-1/2 z-10 bg-gray-800 text-neon border border-neon/30 transform-gpu' 
                      : 'left-1/2 translate-x-[-45%] rotate-[5deg] z-0 bg-gray-900/80 text-gray-400 hover:text-white transform-gpu'
                  }`}
                >
                  <Code className="h-4 w-4" />
                  {isRTL ? 'محرر الكود' : 'Code Editor'}
                </div>
              </div>
              
              {/* Windows Container with Angled Borders */}
              <div className="relative w-full h-[500px]">
                {/* Angled Background Borders - Removed */}
                
                {/* Windows Views */}
                <AnimatePresence mode="wait">
                  {activeView === 'code' ? (
                    <motion.div
                      key="code-editor"
                      initial={{ 
                        opacity: 0, 
                        rotateX: -15,
                        rotateY: 5,
                        y: 30,
                        scale: 0.95,
                        filter: "blur(5px)"
                      }}
                      animate={{ 
                        opacity: 1, 
                        rotateX: 0,
                        rotateY: 0,
                        y: 0,
                        scale: 1,
                        filter: "blur(0px)"
                      }}
                      exit={{ 
                        opacity: 0,
                        rotateX: 15,
                        rotateY: -5,
                        y: -30,
                        scale: 0.95,
                        filter: "blur(5px)"
                      }}
                      transition={{ 
                        type: "spring", 
                        stiffness: 250, 
                        damping: 25,
                        mass: 1.2
                      }}
                      className="absolute inset-0 code-editor bg-gray-950 border border-gray-800 rounded-lg overflow-hidden shadow-2xl"
                    >
                      {/* Editor Header */}
                      <div className="editor-header bg-gray-900 p-2 flex items-center">
                        <div className="flex border-b border-transparent overflow-x-auto hide-scrollbar">
                          {filenames.map((filename, index) => (
                            <button
                              key={index}
                              onClick={() => handleTabChange(index)}
                              className={`px-3 py-1 text-xs font-mono rounded-t-md flex items-center gap-2 ${
                                activeTab === index 
                                  ? 'bg-gray-800 text-white border border-gray-700 border-b-0' 
                                  : 'text-gray-400 hover:text-gray-300'
                              }`}
                            >
                              {fileIcons[index]}
                              {filename}
                            </button>
                          ))}
                        </div>
                        <div className="ml-auto flex space-x-2">
                          <button 
                            onClick={() => handleShowPreview()}
                            className={`p-1 rounded ${showPreview ? 'text-neon' : 'text-gray-400 hover:text-white'} transition-colors`}
                            title={isRTL ? (showPreview ? "إخفاء المعاينة" : "عرض المعاينة") : (showPreview ? "Hide Preview" : "Show Preview")}
                          >
                            {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </button>
                          <button 
                            onClick={handleExportFiles}
                            className="p-1 text-gray-400 hover:text-white transition-colors rounded"
                            title={isRTL ? "تصدير الملفات كملف مضغوط" : "Export files as ZIP"}
                          >
                            <Archive className="h-4 w-4" />
                          </button>
                          <div className="relative group">
                            <button 
                              className="p-1 text-gray-400 hover:text-white transition-colors rounded"
                              title={isRTL ? "تنزيل الملف الحالي" : "Download current file"}
                            >
                              <Download className="h-4 w-4" />
                            </button>
                            <div className="absolute right-0 mt-1 bg-gray-800 border border-gray-700 rounded-md shadow-lg scale-0 group-hover:scale-100 transition-transform origin-top-right z-50">
                              <div className="py-1">
                                <button 
                                  onClick={() => downloadSingleFile(codeContent[0], "index.html")}
                                  className="flex items-center w-full px-4 py-2 text-xs text-left hover:bg-gray-700"
                                >
                                  <Globe className="w-3 h-3 text-orange-400 mr-2" />
                                  index.html
                                </button>
                                <button 
                                  onClick={() => downloadSingleFile(codeContent[1], "styles.css")}
                                  className="flex items-center w-full px-4 py-2 text-xs text-left hover:bg-gray-700"
                                >
                                  <FileText className="w-3 h-3 text-blue-400 mr-2" />
                                  styles.css
                                </button>
                                <button 
                                  onClick={() => downloadSingleFile(codeContent[2], "script.js")}
                                  className="flex items-center w-full px-4 py-2 text-xs text-left hover:bg-gray-700"
                                >
                                  <FileCode className="w-3 h-3 text-yellow-400 mr-2" />
                                  script.js
                                </button>
                              </div>
                            </div>
                          </div>
                          <button 
                            onClick={handleCopyCode}
                            className="p-1 text-gray-400 hover:text-white transition-colors rounded"
                            title="نسخ الكود"
                          >
                            {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                          </button>
                          <button
                            onClick={() => setIsEditing(!isEditing)}
                            className={`p-1 rounded ${isEditing ? 'text-neon' : 'text-gray-400 hover:text-white'} transition-colors`}
                            title="تحرير الكود"
                          >
                            <Code className="h-4 w-4" />
                          </button>
                          <button
                            onClick={handleSwitchView}
                            className="p-1 text-gray-400 hover:text-neon transition-colors rounded"
                            title="التبديل إلى سطر الاوامر"
                          >
                            <Terminal className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      
                      {/* Editor Body */}
                      <div className="p-4 editor-body" style={{ height: 'calc(100% - 87px)' }}>
                        <div className={`flex h-full ${showPreview ? 'flex-col md:flex-row' : ''}`}>
                          {/* Code section */}
                          <div className={`flex ${showPreview ? 'h-1/2 md:h-full md:w-1/2 md:pr-2' : 'h-full w-full'}`}>
                            {/* Line numbers */}
                            <div className="line-numbers text-right pr-4 font-mono text-gray-500 select-none min-w-[30px] overflow-y-auto hide-scrollbar">
                              {Array.from({ length: codeContent[activeTab].split('\n').length }).map((_, i) => (
                                <div key={i} className="text-xs">{i + 1}</div>
                              ))}
                            </div>
                            
                            {/* Code area */}
                            <div className="flex-1 overflow-auto relative hide-scrollbar">
                              {isEditing ? (
                                <div className="relative w-full h-full">
                                  <textarea
                                    value={codeContent[activeTab]}
                                    onChange={handleTextareaInput}
                                    onKeyDown={handleTextareaKeyDown}
                                    onSelect={handleTextareaSelect}
                                    className="w-full h-full bg-transparent text-white font-mono text-sm outline-none resize-none z-10 relative hide-scrollbar"
                                    spellCheck="false"
                                  />
                                  {/* Line highlight */}
                                  {highlightLine >= 0 && (
                                    <div 
                                      className="absolute top-0 left-0 right-0 h-5 bg-gray-800/60 z-0 pointer-events-none"
                                      style={{ 
                                        transform: `translateY(${highlightLine * 20}px)`,
                                        height: '20px' 
                                      }}
                                    ></div>
                                  )}
                                  {/* Suggestion dropdown */}
                                  {showSuggestions && (
                                    <div className="absolute z-20 bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-40 overflow-y-auto hide-scrollbar w-48">
                                      {suggestions.map((suggestion, i) => (
                                        <div 
                                          key={i} 
                                          className="px-3 py-1 text-sm cursor-pointer hover:bg-gray-700"
                                          onClick={() => applySuggestion(suggestion)}
                                        >
                                          {suggestion}
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <pre className="text-white font-mono text-sm">
                                  <code 
                                    className="language-typescript"
                                    dangerouslySetInnerHTML={{ 
                                      __html: highlightSyntax(displayText) + '<span class="text-neon inline-block w-2 animate-blink">|</span>' 
                                    }}
                                  ></code>
                                </pre>
                              )}
                            </div>
                          </div>
                          
                          {/* Preview section */}
                          {showPreview && (
                            <div className="h-1/2 md:h-full md:w-1/2 md:pl-2 mt-4 md:mt-0 border-t md:border-t-0 md:border-l border-gray-700 pt-4 md:pt-0 md:pl-4">
                              <div className="bg-gray-950 rounded-md h-full overflow-auto hide-scrollbar">
                                {previewLoading ? (
                                  <div className="flex items-center justify-center h-full">
                                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-neon"></div>
                                  </div>
                                ) : previewError ? (
                                  <div className="preview-container">
                                    <div className="preview-header bg-gray-800 px-3 py-2 border-b border-gray-700 mb-4 rounded-t-md flex items-center">
                                      <span className="text-xs text-gray-300">Preview Error</span>
                                    </div>
                                    
                                    <div className="preview-content bg-gray-900 p-6">
                                      <div className="bg-red-900/30 border border-red-900/50 p-4 rounded-lg text-red-300 max-w-md mx-auto">
                                        <h3 className="text-lg font-medium mb-2">Rendering Error</h3>
                                        <p className="text-sm">{previewError}</p>
                                      </div>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="preview-container h-full">
                                    <div className="preview-header bg-gray-800 px-3 py-2 border-b border-gray-700 flex items-center justify-between">
                                      <div className="flex items-center">
                                        <span className="text-xs text-gray-300">{isRTL ? "المعاينة المباشرة" : "Live Preview"}</span>
                                      </div>
                                      <button 
                                        onClick={updatePreview}
                                        className="text-gray-400 hover:text-neon p-1 rounded"
                                        title={isRTL ? "تحديث المعاينة" : "Refresh Preview"}
                                      >
                                        <RefreshCw className="h-3 w-3" />
                                      </button>
                                    </div>
                                    
                                    <div className="preview-frame h-[calc(100%-34px)] bg-white w-full">
                                      <iframe 
                                        ref={iframeRef}
                                        className="w-full h-full border-none hide-scrollbar"
                                        title={isRTL ? "معاينة الكود" : "Code Preview"}
                                        sandbox="allow-scripts"
                                        srcDoc={generatePreview()}
                                        scrolling="no"
                                        style={{ overflow: 'hidden' }}
                                      ></iframe>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* Editor Footer */}
                      <div className="editor-footer bg-gray-900 px-4 py-2 flex justify-between items-center">
                        <div className="text-xs text-gray-400 font-mono">
                          {isEditing ? `Line ${cursorLine + 1}, Column: ${
                            codeContent[activeTab].split('\n')[cursorLine]?.length || 0
                          }` : "Read-only mode"}
                        </div>
                        <div className="flex gap-3">
                          <button
                            className="text-xs text-gray-400 hover:text-neon px-2 py-1 rounded"
                            onClick={() => setIsEditing(!isEditing)}
                          >
                            {isEditing ? "Save" : "Edit"}
                          </button>
                          <span className="text-xs text-gray-500">TypeScript</span>
                          <span className="text-xs text-gray-500">UTF-8</span>
                        </div>
                      </div>
                    </motion.div>
                  ) : (
                    <motion.div
                      key="terminal"
                      initial={{ 
                        opacity: 0, 
                        rotateX: 15,
                        rotateY: -5,
                        y: -30,
                        scale: 0.95,
                        filter: "blur(5px)"
                      }}
                      animate={{ 
                        opacity: 1, 
                        rotateX: 0,
                        rotateY: 0,
                        y: 0,
                        scale: 1,
                        filter: "blur(0px)"
                      }}
                      exit={{ 
                        opacity: 0,
                        rotateX: -15,
                        rotateY: 5,
                        y: 30,
                        scale: 0.95,
                        filter: "blur(5px)"
                      }}
                      transition={{ 
                        type: "spring", 
                        stiffness: 250, 
                        damping: 25,
                        mass: 1.2
                      }}
                      className="absolute inset-0 terminal-window bg-gray-950 border border-gray-800 rounded-lg overflow-hidden shadow-2xl"
                    >
                      {/* Terminal Header */}
                      <div className="terminal-header bg-gray-900 p-2 flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="text-xs text-gray-400 font-mono">
                            {isRTL ? "سطر الاوامر" : "Terminal"}
                          </div>
                        </div>
                      </div>
                      
                      {/* Terminal Body */}
                      <div 
                        className="p-4 terminal-body font-mono text-sm overflow-auto hide-scrollbar" 
                        style={{ height: 'calc(100% - 34px)', backgroundColor: '#0d1117' }}
                        onClick={() => {
                          // Focus the input when clicking anywhere in the terminal
                          const inputElement = document.querySelector('.terminal-input') as HTMLInputElement;
                          if (inputElement) inputElement.focus();
                        }}
                      >
                        {terminalHistory.map((item, i) => (
                          <div key={i} className={`whitespace-pre-wrap ${
                            item.type === 'command' ? 'text-neon font-bold mb-1' : 
                            item.type === 'error' ? 'text-red-400 mb-3' :
                            item.type === 'banner' ? 'text-cyan-300 my-2' : 'text-gray-200 mb-3'
                          }`}>
                            {item.content}
                          </div>
                        ))}
                        <div className="flex items-center w-full">
                          <span className="text-green-400 font-bold ml-2">{terminalUser}@{terminalHost}</span>
                          <span className="text-blue-400 font-bold ml-2">:{terminalPath}</span>
                          <span className="text-neon font-bold ml-1">$</span>
                          <input
                            type="text"
                            value={terminalCommand}
                            onChange={(e) => setTerminalCommand(e.target.value)}
                            onKeyDown={handleTerminalCommand}
                            className="bg-transparent flex-1 outline-none text-white caret-neon terminal-input w-full"
                            autoFocus={activeView === 'terminal'}
                            placeholder={isRTL ? "اكتب -م للمساعدة..." : "Type -h for help..."}
                          />
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
      
      <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 hidden md:block">
        <motion.a 
          href="#about"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            duration: 0.5, 
            delay: 1.5,
            y: {
              duration: 0.8,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }
          }}
          className="flex flex-col items-center text-gray-400 hover:text-neon transition-colors"
        >
          <span className="text-sm mb-2">{t('hero.scrollDown')}</span>
          <ArrowDown className="h-4 w-4" />
        </motion.a>
      </div>
    </section>
  );
}
